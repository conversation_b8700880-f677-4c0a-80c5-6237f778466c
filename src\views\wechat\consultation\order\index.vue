<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="咨询师" prop="consultantId">
        <el-select v-model="queryParams.consultantId" placeholder="请选择咨询师" clearable style="width: 240px">
          <el-option v-for="consultant in consultantList" :key="consultant.id" :label="consultant.name"
            :value="consultant.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable style="width: 240px">
          <el-option label="待支付" value="pending" />
          <el-option label="已支付" value="paid" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
          <el-option label="已退款" value="refunded" />
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态" prop="paymentStatus">
        <el-select v-model="queryParams.paymentStatus" placeholder="请选择支付状态" clearable style="width: 240px">
          <el-option label="未支付" value="unpaid" />
          <el-option label="已支付" value="paid" />
          <el-option label="支付失败" value="failed" />
          <el-option label="已退款" value="refunded" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:consultantOrder:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:consultantOrder:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:consultantOrder:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:consultantOrder:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Clock" @click="handleProcessExpired"
          v-hasPermi="['system:consultantOrder:edit']">处理过期订单</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单ID" align="center" prop="id" width="80" />
      <el-table-column label="订单号" align="center" prop="orderNo" width="180" />
      <el-table-column label="用户" align="center" prop="userName" width="120">
        <template #default="scope">
          <span>{{ scope.row.userName || `用户${scope.row.userId}` }}</span>
        </template>
      </el-table-column>
      <el-table-column label="咨询师" align="center" prop="consultantName" width="120">
        <template #default="scope">
          <span>{{ scope.row.consultantName || `咨询师${scope.row.consultantId}` }}</span>
        </template>
      </el-table-column>
      <el-table-column label="咨询时间" align="center" width="160">
        <template #default="scope">
          <div>{{ parseTime(scope.row.appointmentTime, '{y}-{m}-{d}') }}</div>
          <div>{{ parseTime(scope.row.appointmentTime, '{h}:{i}') }} - {{ parseTime(scope.row.endTime, '{h}:{i}') }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="咨询类型" align="center" prop="consultType" width="100">
        <template #default="scope">
          <dict-tag :options="sys_consult_type" :value="scope.row.consultType" />
        </template>
      </el-table-column>
      <el-table-column label="订单金额" align="center" prop="totalAmount" width="100">
        <template #default="scope">
          <span>¥{{ scope.row.totalAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付金额" align="center" prop="paymentAmount" width="100">
        <template #default="scope">
          <span>¥{{ scope.row.paymentAmount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="paymentStatus" width="100">
        <template #default="scope">
          <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)">
            {{ getPaymentStatusText(scope.row.paymentStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:consultantOrder:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:consultantOrder:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" trigger="click" :teleported="true">
            <el-button link type="primary">
              <el-icon><more-filled /></el-icon>更多
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <div v-hasPermi="['system:consultantOrder:edit']">
                  <el-dropdown-item command="updateStatus" tabindex="0">更新状态</el-dropdown-item>
                </div>
                <div v-hasPermi="['system:consultantOrder:edit']">
                  <el-dropdown-item command="cancel" tabindex="0">取消订单</el-dropdown-item>
                </div>
                <div v-hasPermi="['system:consultantOrder:edit']">
                  <el-dropdown-item command="refund" tabindex="0">退款</el-dropdown-item>
                </div>
                <div v-hasPermi="['system:consultantOrder:remove']">
                  <el-dropdown-item command="delete" tabindex="0">删除</el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改咨询订单对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="orderRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户ID" prop="userId">
              <el-input v-model="form.userId" placeholder="请输入用户ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="咨询师" prop="consultantId">
              <el-select v-model="form.consultantId" placeholder="请选择咨询师" style="width: 100%">
                <el-option v-for="consultant in consultantList" :key="consultant.id" :label="consultant.name"
                  :value="consultant.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="咨询时间" prop="appointmentTime">
              <el-date-picker v-model="form.appointmentTime" type="datetime" placeholder="选择咨询时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker v-model="form.endTime" type="datetime" placeholder="选择结束时间" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="咨询类型" prop="consultType">
              <el-select v-model="form.consultType" placeholder="请选择咨询类型" style="width: 100%">
                <el-option v-for="dict in sys_consult_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="咨询时长" prop="duration">
              <el-input-number v-model="form.duration" :min="30" :max="180" :step="30" placeholder="咨询时长（分钟）"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单金额" prop="totalAmount">
              <el-input-number v-model="form.totalAmount" :precision="2" :min="0" :max="99999" placeholder="订单金额"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付金额" prop="paymentAmount">
              <el-input-number v-model="form.paymentAmount" :precision="2" :min="0" :max="99999" placeholder="支付金额"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择订单状态" style="width: 100%">
                <el-option label="待支付" value="pending" />
                <el-option label="已支付" value="paid" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
                <el-option label="已退款" value="refunded" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付方式" prop="paymentMethod">
              <el-select v-model="form.paymentMethod" placeholder="请选择支付方式" style="width: 100%">
                <el-option v-for="dict in sys_payment_method" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ConsultantOrder">
import {
  listConsultantOrder,
  getConsultantOrder,
  delConsultantOrder,
  addConsultantOrder,
  updateConsultantOrder,
  exportConsultantOrder,
  updateConsultantOrderStatus,
  cancelConsultantOrder,
  refundConsultantOrder,
  processExpiredConsultantOrders
} from "@/api/wechat/consultation/order";
import { listAllSimpleConsultants } from "@/api/wechat/consultation/consultant";
import { listUser } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { sys_consult_type, sys_payment_method } = proxy.useDict("sys_consult_type", "sys_payment_method");

const orderList = ref([]);
const consultantList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: null,
    userId: null,
    consultantId: null,
    status: null,
    paymentStatus: null
  },
  rules: {
    userId: [
      { required: true, message: "用户ID不能为空", trigger: "blur" }
    ],
    consultantId: [
      { required: true, message: "咨询师不能为空", trigger: "change" }
    ],
    appointmentTime: [
      { required: true, message: "咨询时间不能为空", trigger: "change" }
    ],
    endTime: [
      { required: true, message: "结束时间不能为空", trigger: "change" }
    ],
    consultType: [
      { required: true, message: "咨询类型不能为空", trigger: "change" }
    ],
    duration: [
      { required: true, message: "咨询时长不能为空", trigger: "blur" }
    ],
    totalAmount: [
      { required: true, message: "订单金额不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询咨询订单列表 */
function getList() {
  loading.value = true;
  listConsultantOrder(queryParams.value).then(response => {
    orderList.value = response.rows;
    total.value = response.total;
    loading.value = false;

    // 获取用户和咨询师名称
    fetchUserAndConsultantNames();
  });
}

/** 获取用户和咨询师名称 */
async function fetchUserAndConsultantNames() {
  const userIds = [...new Set(orderList.value.map(item => item.userId).filter(id => id))];
  const consultantIds = [...new Set(orderList.value.map(item => item.consultantId).filter(id => id))];

  // 获取用户信息
  if (userIds.length > 0) {
    try {
      const userResponse = await listUser({ userIds: userIds.join(',') });
      const userMap = {};
      userResponse.rows.forEach(user => {
        userMap[user.userId] = user.nickName || user.userName;
      });

      orderList.value.forEach(order => {
        if (userMap[order.userId]) {
          order.userName = userMap[order.userId];
        }
      });
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  }

  // 获取咨询师信息
  if (consultantIds.length > 0) {
    consultantList.value.forEach(consultant => {
      orderList.value.forEach(order => {
        if (order.consultantId === consultant.id) {
          order.consultantName = consultant.name;
        }
      });
    });
  }
}

/** 获取咨询师列表 */
function getConsultantList() {
  listAllSimpleConsultants().then(response => {
    consultantList.value = response.data;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: null,
    orderNo: null,
    userId: null,
    consultantId: null,
    appointmentTime: null,
    endTime: null,
    consultType: null,
    duration: 60,
    totalAmount: null,
    paymentAmount: null,
    status: "pending",
    paymentMethod: null,
    remark: null
  };
  proxy.resetForm("orderRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加咨询订单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getConsultantOrder(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改咨询订单";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["orderRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateConsultantOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addConsultantOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const orderIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除咨询订单编号为"' + orderIds + '"的数据项？').then(function () {
    return delConsultantOrder(orderIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/consultantOrder/export', {
    ...queryParams.value
  }, `order_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleDetail(row) {
  // 这里可以跳转到详情页面或打开详情对话框
  proxy.$router.push(`/wechat/consultation/order/detail/${row.id}`);
}

/** 处理过期订单 */
function handleProcessExpired() {
  proxy.$modal.confirm('确认要处理所有过期订单吗？').then(function () {
    return processExpiredConsultantOrders();
  }).then((response) => {
    proxy.$modal.msgSuccess(response.msg || "处理成功");
    getList();
  }).catch(() => { });
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case "updateStatus":
      handleUpdateStatus(row);
      break;
    case "cancel":
      handleCancel(row);
      break;
    case "refund":
      handleRefund(row);
      break;
    case "delete":
      handleDelete(row);
      break;
    default:
      break;
  }
}

/** 更新状态 */
function handleUpdateStatus(row) {
  proxy.$prompt('请输入新的订单状态', '更新状态', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValidator: (value) => {
      if (!value) {
        return '状态不能为空';
      }
      return true;
    }
  }).then(({ value }) => {
    updateConsultantOrderStatus(row.id, value).then(() => {
      proxy.$modal.msgSuccess("状态更新成功");
      getList();
    });
  }).catch(() => { });
}

/** 取消订单 */
function handleCancel(row) {
  proxy.$prompt('请输入取消原因', '取消订单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValidator: (value) => {
      if (!value) {
        return '取消原因不能为空';
      }
      return true;
    }
  }).then(({ value }) => {
    cancelConsultantOrder(row.id, value).then(() => {
      proxy.$modal.msgSuccess("订单取消成功");
      getList();
    });
  }).catch(() => { });
}

/** 退款订单 */
function handleRefund(row) {
  proxy.$prompt('请输入退款金额', '退款订单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'number',
    inputValidator: (value) => {
      if (!value || value <= 0) {
        return '退款金额必须大于0';
      }
      if (value > row.paymentAmount) {
        return '退款金额不能超过支付金额';
      }
      return true;
    }
  }).then(({ value }) => {
    proxy.$prompt('请输入退款原因', '退款原因', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValidator: (reason) => {
        if (!reason) {
          return '退款原因不能为空';
        }
        return true;
      }
    }).then(({ value: reason }) => {
      refundConsultantOrder(row.id, parseFloat(value), reason).then(() => {
        proxy.$modal.msgSuccess("退款成功");
        getList();
      });
    });
  }).catch(() => { });
}

/** 获取订单状态类型 */
function getStatusType(status) {
  const statusMap = {
    'pending': 'warning',
    'paid': 'success',
    'completed': 'success',
    'cancelled': 'danger',
    'refunded': 'info'
  };
  return statusMap[status] || 'info';
}

/** 获取订单状态文本 */
function getStatusText(status) {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'completed': '已完成',
    'cancelled': '已取消',
    'refunded': '已退款'
  };
  return statusMap[status] || status;
}

/** 获取支付状态类型 */
function getPaymentStatusType(status) {
  const statusMap = {
    'unpaid': 'warning',
    'paid': 'success',
    'failed': 'danger',
    'refunded': 'info'
  };
  return statusMap[status] || 'info';
}

/** 获取支付状态文本 */
function getPaymentStatusText(status) {
  const statusMap = {
    'unpaid': '未支付',
    'paid': '已支付',
    'failed': '支付失败',
    'refunded': '已退款'
  };
  return statusMap[status] || status;
}

onMounted(() => {
  getList();
  getConsultantList();
});
</script>
