import request from '@/utils/request'

// 查询菜单列表
export function listMenu(query) {
  return request({
    url: '/wechat/tabbar/list',
    method: 'get',
    params: query
  })
}

// 根据权限查询菜单列表
export function listMenuByPermissions(permissions) {
  return request({
    url: '/wechat/tabbar/listByPermissions',
    method: 'get',
    params: { permissions }
  })
}

// 获取菜单详细信息
export function getMenu(id) {
  return request({
    url: '/wechat/tabbar/' + id,
    method: 'get'
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/wechat/tabbar/add',
    method: 'post',
    data: data
  })
}


// 修改菜单
export function updateMenu(data) {
  return request({
    url: '/wechat/tabbar',
    method: 'put',
    data: data
  })
}

// 删除菜单
export function delMenu(ids) {
  return request({
    url: '/wechat/tabbar/' + ids,
    method: 'delete'
  })
}
