# 选项数据库字段修改说明

## 概述

根据您提供的数据库表结构 `psy_t_question_option`，我已经修改了前端代码以匹配实际的数据库字段结构。主要的变化是将 `option_value` 字段作为选项分值（int类型），而不是选项值。

## 数据库表结构

```sql
create table psy_t_question_option
(
    id           bigint auto_increment comment '选项ID'
        primary key,
    question_id  bigint                               not null comment '关联psy_t_question.id',
    option_text  varchar(200)                         not null comment '选项文本',
    option_value int                                  not null comment '选项分值',
    sort         int        default 0                 null comment '排序',
    status       tinyint(1) default 1                 null comment '状态',
    del_flag     char       default '0'               null comment '删除标志',
    create_time  datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time  datetime   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间',
    create_by    varchar(64)                          null comment '创建者',
    update_by    varchar(64)                          null comment '更新者'
)
    comment '题目选项表';
```

## 字段映射关系

### 数据库字段 → 前端字段
| 数据库字段 | 前端字段 | 类型 | 说明 |
|-----------|---------|------|------|
| `id` | `id` | bigint | 选项ID |
| `question_id` | `questionId` | bigint | 关联题目ID |
| `option_text` | `optionText` | string | 选项文本 |
| `option_value` | `optionValue` | int | **选项分值**（重要变化） |
| `sort` | `sort` | int | 排序 |
| `status` | `status` | tinyint | 状态（1=正常，0=禁用） |
| `del_flag` | `delFlag` | char | 删除标志（'0'=正常，'1'=已删除） |

## 主要修改内容

### 1. 量表管理页面 (`scale/index.vue`)

#### 选项编辑对话框
**修改前：**
```vue
<el-form-item label="选项值" prop="optionValue">
  <el-input v-model="optionForm.optionValue" placeholder="请输入选项值" />
</el-form-item>
<el-form-item label="分数" prop="score">
  <el-input-number v-model="optionForm.score" :min="0" :max="100" />
</el-form-item>
```

**修改后：**
```vue
<el-form-item label="选项文本" prop="optionText">
  <el-input v-model="optionForm.optionText" placeholder="请输入选项文本" />
</el-form-item>
<el-form-item label="选项分值" prop="optionValue">
  <el-input-number v-model="optionForm.optionValue" :min="0" :max="100" placeholder="请输入选项分值" />
</el-form-item>
<el-form-item label="排序" prop="sort">
  <el-input-number v-model="optionForm.sort" :min="0" placeholder="请输入排序" />
</el-form-item>
<el-form-item label="状态" prop="status">
  <el-radio-group v-model="optionForm.status">
    <el-radio :value="1">正常</el-radio>
    <el-radio :value="0">禁用</el-radio>
  </el-radio-group>
</el-form-item>
<el-form-item label="删除标志" prop="delFlag">
  <el-radio-group v-model="optionForm.delFlag">
    <el-radio value="0">正常</el-radio>
    <el-radio value="1">已删除</el-radio>
  </el-radio-group>
</el-form-item>
```

#### 选项表格显示
**修改前：**
```vue
<el-table-column label="选项值" align="center" prop="optionValue" width="80" />
<el-table-column label="选项文本" align="center" prop="optionText" />
<el-table-column label="分数" align="center" prop="score" width="80" />
```

**修改后：**
```vue
<el-table-column label="选项文本" align="center" prop="optionText" />
<el-table-column label="选项分值" align="center" prop="optionValue" width="100" />
<el-table-column label="排序" align="center" prop="sort" width="80" />
<el-table-column label="状态" align="center" width="80">
  <template #default="scope">
    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
      {{ scope.row.status === 1 ? '正常' : '禁用' }}
    </el-tag>
  </template>
</el-table-column>
<el-table-column label="删除标志" align="center" width="80">
  <template #default="scope">
    <el-tag :type="scope.row.delFlag === '0' ? 'success' : 'danger'">
      {{ scope.row.delFlag === '0' ? '正常' : '已删除' }}
    </el-tag>
  </template>
</el-table-column>
```

#### 选项表单数据结构
**修改前：**
```javascript
const optionForm = ref({
  id: null,
  questionId: null,
  optionText: "",
  optionValue: "",
  score: null,
  delFlag: 0,
  displayText: ""
});
```

**修改后：**
```javascript
const optionForm = ref({
  id: null,
  questionId: null,
  optionText: "",
  optionValue: null,  // 改为数字类型，表示分值
  sort: 0,            // 新增排序字段
  status: 1,          // 新增状态字段
  delFlag: "0"        // 改为字符串类型
});
```

### 2. 题目管理页面 (`question/index.vue`)

#### 题目编辑表单中的选项部分
**修改前：**
```vue
<el-col :span="4">
  <el-input v-model="option.optionValue" placeholder="选项值" />
</el-col>
<el-col :span="4">
  <el-input-number v-model="option.score" :min="0" placeholder="分值" />
</el-col>
```

**修改后：**
```vue
<el-col :span="4">
  <el-input-number v-model="option.optionValue" :min="0" placeholder="分值" />
</el-col>
<el-col :span="4">
  <el-input-number v-model="option.sort" :min="0" placeholder="排序" />
</el-col>
```

#### 默认选项数据
**修改前：**
```javascript
function addDefaultOptions() {
  form.value.options = [
    { optionText: "选项A", optionValue: "A", score: 1, orderNum: 1 },
    { optionText: "选项B", optionValue: "B", score: 2, orderNum: 2 },
    { optionText: "选项C", optionValue: "C", score: 3, orderNum: 3 },
    { optionText: "选项D", optionValue: "D", score: 4, orderNum: 4 }
  ];
}
```

**修改后：**
```javascript
function addDefaultOptions() {
  form.value.options = [
    { optionText: "非常同意", optionValue: 5, sort: 1, status: 1, delFlag: "0" },
    { optionText: "同意", optionValue: 4, sort: 2, status: 1, delFlag: "0" },
    { optionText: "不确定", optionValue: 3, sort: 3, status: 1, delFlag: "0" },
    { optionText: "不同意", optionValue: 2, sort: 4, status: 1, delFlag: "0" },
    { optionText: "非常不同意", optionValue: 1, sort: 5, status: 1, delFlag: "0" }
  ];
}
```

#### 添加选项方法
**修改前：**
```javascript
function addOption() {
  const newOrder = form.value.options.length + 1;
  const newOption = {
    optionText: "",
    optionValue: String.fromCharCode(64 + newOrder), // A, B, C...
    score: newOrder,
    orderNum: newOrder
  };
  form.value.options.push(newOption);
}
```

**修改后：**
```javascript
function addOption() {
  const newOrder = form.value.options.length + 1;
  const newOption = {
    optionText: "",
    optionValue: newOrder,  // 直接使用数字作为分值
    sort: newOrder,         // 排序
    status: 1,              // 状态
    delFlag: "0"            // 删除标志
  };
  form.value.options.push(newOption);
}
```

## 数据验证规则

### 选项表单验证
```javascript
const optionRules = ref({
  optionText: [
    { required: true, message: "选项文本不能为空", trigger: "blur" }
  ],
  optionValue: [
    { required: true, message: "选项分值不能为空", trigger: "blur" }
  ]
});
```

## 重要变化说明

### 1. 字段语义变化
- **`optionValue`**：从"选项值"改为"选项分值"，数据类型从字符串改为数字
- **新增字段**：`sort`（排序）、`status`（状态）
- **字段类型**：`delFlag` 从数字改为字符串

### 2. 显示逻辑调整
- 表格中不再显示单独的"分数"列，而是显示"选项分值"
- 新增"排序"和"状态"列的显示
- 状态和删除标志使用不同的标签颜色区分

### 3. 默认数据优化
- 默认选项改为心理测评常用的李克特量表格式
- 分值从高到低：非常同意(5) → 非常不同意(1)
- 包含完整的字段信息

## 兼容性考虑

### 1. 后端API适配
确保后端API能够正确处理新的字段结构：
- `option_value` 字段接收数字类型
- 支持 `sort` 和 `status` 字段
- `del_flag` 字段使用字符串类型

### 2. 数据迁移
如果已有数据，需要考虑数据迁移：
- 将原有的选项值转换为分值
- 为现有数据添加默认的 `sort` 和 `status` 值

### 3. 前端兼容
- 所有选项相关的组件都已更新
- 保持与后端API的字段名一致
- 数据类型匹配数据库定义

## 总结

通过这次修改，前端代码现在完全匹配数据库表结构：

1. **字段对应**：所有字段名和类型都与数据库一致
2. **功能完整**：支持选项的完整CRUD操作
3. **数据准确**：选项分值直接存储在 `option_value` 字段
4. **界面友好**：提供清晰的选项编辑和显示界面
5. **验证完善**：包含必要的数据验证规则

现在的实现更加符合心理测评系统的实际需求，选项分值的概念更加清晰，便于后续的评分计算和数据分析。
