# 量表管理页面题目编辑优化说明

## 概述

根据您的要求，我已经对量表管理页面进行了重大优化：
1. 在题目编辑弹框下面直接展示选项，不再需要单独的"查看选项"按钮
2. 完善了题目表格的字段显示，使其类似题目管理页面的表格
3. 提升了用户体验和操作效率

## 主要改进

### 1. 题目表格字段完善

#### 新增字段显示
- ✅ **ID字段**：显示题目的唯一标识
- ✅ **状态字段**：显示题目的删除状态（正常/已删除）
- ✅ **完整字段展示**：ID、题号、题目内容、题目类型、子量表、是否必答、反向计分、排序、选项数量、状态

#### 字段对比
| 字段 | 优化前 | 优化后 |
|------|--------|--------|
| ID | ❌ 无 | ✅ 显示 |
| 题号 | ✅ 有 | ✅ 保持 |
| 题目内容 | ✅ 有 | ✅ 保持 |
| 题目类型 | ✅ 有 | ✅ 保持 |
| 子量表 | ✅ 有 | ✅ 保持 |
| 是否必答 | ✅ 有 | ✅ 保持 |
| 反向计分 | ✅ 有 | ✅ 保持 |
| 排序 | ✅ 有 | ✅ 保持 |
| 选项数量 | ✅ 有 | ✅ 保持 |
| 状态 | ❌ 无 | ✅ 新增 |

#### 操作按钮优化
**优化前：**
```vue
<el-button link type="primary" icon="View" @click="handleViewQuestionOptions(scope.row)">查看选项</el-button>
<el-button link type="primary" icon="Edit" @click="handleUpdateQuestion(scope.row)">修改</el-button>
<el-button link type="primary" icon="Delete" @click="handleDeleteQuestion(scope.row)">删除</el-button>
```

**优化后：**
```vue
<el-button link type="primary" icon="Edit" @click="handleUpdateQuestion(scope.row)">修改</el-button>
<el-button link type="primary" icon="Delete" @click="handleDeleteQuestion(scope.row)">删除</el-button>
```

### 2. 题目编辑对话框集成选项管理

#### 对话框宽度优化
- **优化前**：800px
- **优化后**：1200px（为选项表格提供更多空间）

#### 选项管理集成
在题目编辑表单下方直接集成选项管理功能：

```vue
<!-- 选项管理 -->
<el-divider content-position="left">选项管理</el-divider>
<div style="margin-bottom: 20px;">
  <el-button type="primary" size="small" icon="Plus" @click="handleAddOption"
    v-hasPermi="['system:assessment:question:edit']">新增选项</el-button>
</div>
<el-table :data="questionForm.optionList" style="width: 100%">
  <!-- 选项表格列定义 -->
</el-table>
```

#### 选项表格完整显示
- **选项值**：`optionValue`
- **选项文本**：`optionText`
- **显示文本**：`displayText`
- **分数**：`score`
- **状态**：`delFlag`（正常/已删除）
- **创建时间**：格式化显示
- **操作**：编辑、删除按钮

### 3. 数据流优化

#### 数据源统一
**优化前：**
- 题目数据：`currentQuestion`
- 选项数据：需要单独获取

**优化后：**
- 题目数据：`questionForm`
- 选项数据：`questionForm.optionList`（统一数据源）

#### 方法调整
```javascript
// 优化前
function handleAddOption() {
  optionForm.value.questionId = currentQuestion.value.id;
}

// 优化后
function handleAddOption() {
  optionForm.value.questionId = questionForm.value.id;
}
```

#### 数据刷新机制
```javascript
function refreshCurrentQuestionOptions() {
  if (questionForm.value.id) {
    getOptionsByQuestion(questionForm.value.id).then(response => {
      questionForm.value.optionList = response.data || [];
      // 同时更新题目列表中的选项数量
      const questionIndex = questionList.value.findIndex(q => q.id === questionForm.value.id);
      if (questionIndex !== -1) {
        questionList.value[questionIndex].optionList = response.data || [];
      }
    });
  }
}
```

### 4. 用户体验提升

#### 操作流程简化
**优化前的操作流程：**
1. 在题目列表中点击"修改"按钮
2. 在题目编辑对话框中修改题目信息
3. 关闭题目编辑对话框
4. 在题目列表中点击"查看选项"按钮
5. 在选项查看对话框中管理选项

**优化后的操作流程：**
1. 在题目列表中点击"修改"按钮
2. 在题目编辑对话框中修改题目信息
3. 在同一对话框下方直接管理选项
4. 一次性完成题目和选项的编辑

#### 界面布局优化
- **分隔线**：使用 `el-divider` 清晰分隔题目信息和选项管理
- **空状态处理**：无选项时显示友好的空状态和新增按钮
- **响应式设计**：更大的对话框宽度适应选项表格

### 5. 代码清理

#### 移除不再使用的代码
- ✅ 移除选项查看对话框
- ✅ 移除 `optionDialogOpen` 变量
- ✅ 移除 `currentQuestion` 变量
- ✅ 移除 `handleViewQuestionOptions` 方法

#### 保留的功能
- ✅ 选项编辑对话框（用于新增/编辑单个选项）
- ✅ 选项CRUD操作
- ✅ 数据验证和错误处理
- ✅ 权限控制

### 6. 技术特点

#### 数据一致性
- 统一使用 `questionForm` 作为数据源
- 选项操作后实时更新 `questionForm.optionList`
- 保持题目列表和编辑表单的数据同步

#### 性能优化
- 减少对话框数量，降低内存占用
- 统一数据管理，减少数据传递
- 优化用户操作路径，提升效率

#### 用户体验
- 一站式编辑体验
- 直观的界面布局
- 即时的操作反馈

## 使用说明

### 1. 查看和编辑题目
1. 在量表管理页面点击"题目管理"
2. 在题目列表中点击"修改"按钮
3. 在弹出的对话框中编辑题目信息
4. 在对话框下方直接查看和管理选项

### 2. 管理选项
1. 在题目编辑对话框的选项管理区域
2. 点击"新增选项"添加新选项
3. 点击选项行的"编辑"按钮修改选项
4. 点击选项行的"删除"按钮删除选项

### 3. 完成编辑
1. 完成题目和选项的编辑后
2. 点击"确定"保存所有更改
3. 系统会同时保存题目信息和选项数据

## 技术优势

### 1. 集成化设计
- 题目和选项管理集成在一个界面
- 减少界面跳转和操作步骤
- 提供一致的用户体验

### 2. 数据统一管理
- 使用统一的数据源
- 简化数据流转逻辑
- 提高数据一致性

### 3. 界面优化
- 更大的对话框空间
- 清晰的功能分区
- 友好的空状态处理

### 4. 代码简化
- 移除冗余代码
- 统一方法命名
- 提高代码可维护性

## 总结

通过这次优化，量表管理页面的题目编辑功能变得更加高效和用户友好：

1. **操作简化**：从多步操作简化为一站式编辑
2. **界面优化**：更完整的字段显示和更好的布局
3. **功能集成**：题目和选项管理集成在一个对话框
4. **体验提升**：减少界面跳转，提高操作效率
5. **代码优化**：移除冗余代码，提高可维护性

现在用户可以在一个对话框中完成题目的完整编辑，包括题目信息和选项管理，大大提升了使用体验和操作效率。
