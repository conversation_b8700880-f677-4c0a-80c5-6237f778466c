# 管理员消息转发修复说明

## 问题描述

在咨询系统中，当管理员代替咨询师发送消息时，出现以下问题：
- 用户可以正常接收到管理员发送的消息
- 但咨询师无法接收到管理员发送的消息
- 导致咨询师不知道管理员已经代替他们回复了用户

## 问题分析

### 原因分析
通过日志分析发现，问题出现在前端发送消息的逻辑：

1. **错误的消息类型**：管理员发送消息时使用了 `type: 'chat'`，而不是 `type: 'admin_reply'`
2. **错误的接收者设置**：消息直接发送给了咨询师（receiverId=136），而不是用户
3. **缺少咨询师转发逻辑**：没有走 `handleAdminReplyMessage` 流程

### 预期流程
管理员代替咨询师发送消息的正确流程应该是：
1. 管理员发送 `admin_reply` 类型消息
2. 消息发送给用户（真正的接收者）
3. 同时转发给咨询师（让咨询师知道管理员已回复）

## 修复内容

### 1. 修复发送消息逻辑

**文件**：`src/store/modules/chat.js`

#### 修复前（第558行）：
```javascript
// 管理员模式下，使用WebSocket发送消息
if (this.userRole === 'admin') {
  // 构建WebSocket消息
  const wsMessage = {
    type: 'chat',  // ❌ 错误：使用了普通聊天类型
    ...messageData,
    senderId: this.userId,
    sendTime: new Date().toISOString(),
    messageId: `temp_${Date.now()}`
  }
}
```

#### 修复后：
```javascript
// 管理员模式下，使用WebSocket发送消息
if (this.userRole === 'admin') {
  // 构建WebSocket消息 - 管理员代替咨询师发送消息
  const wsMessage = {
    type: 'admin_reply',  // ✅ 正确：使用管理员回复类型
    conversationId: this.currentConversation.conversationId,
    userId: this.currentConversation.userId,  // 真正的消息接收者（用户）
    consultantId: this.currentConversation.consultantId,  // 咨询师ID
    content,
    messageType,
    fileUrl,
    senderId: this.userId,  // 管理员ID
    sendTime: new Date().toISOString(),
    messageId: `temp_${Date.now()}`
  }
}
```

### 2. 添加admin_reply消息处理逻辑

在WebSocket消息处理的switch语句中添加了新的case：

```javascript
case 'admin_reply':
  // 管理员代替咨询师发送的消息
  console.log('收到管理员代替咨询师发送的消息:', data)
  
  // 播放消息提示音
  const adminAudio = new Audio('../../../public/audio.mp3')
  adminAudio.play()
  
  // 确保消息数据格式正确
  if (!data.messageId) {
    console.warn('收到的管理员消息缺少messageId', data)
    data.messageId = `temp_${Date.now()}`
  }

  // 更新会话列表中的最后消息信息
  await this.updateConversationLastMessage(data)

  // 如果是当前会话的消息，则添加到消息列表
  if (String(data.conversationId) === String(this.currentConversationId)) {
    // 检查消息是否已存在
    const existingAdminIndex = this.messages.findIndex(m =>
      m.messageId === data.messageId ||
      (m.sendTime === data.sendTime && m.senderId === data.senderId && m.content === data.content)
    )

    if (existingAdminIndex === -1) {
      console.log('添加管理员消息到当前会话:', data)
      // 强制设置响应式更新
      this.messages = [...this.messages, data]

      // 处理未读消息计数和已读标记
      if (data.senderId != this.userId) {
        // 重置当前会话的未读计数
        const conversation = this.conversations.find(c =>
          String(c.conversationId) === String(this.currentConversationId)
        );

        if (conversation) {
          // 根据用户角色，重置相应的未读计数
          if (this.userRole === 'consultant' || this.userRole === 'admin') {
            if (conversation.consultantUnreadCount > 0) {
              conversation.consultantUnreadCount = 0;
              this.markConversationRead(this.currentConversationId);
            }
          } else {
            if (conversation.userUnreadCount > 0) {
              conversation.userUnreadCount = 0;
              this.markConversationRead(this.currentConversationId);
            }
          }
        }
      }

      // 触发自定义事件，通知前端有新消息
      window.dispatchEvent(new CustomEvent('websocket-message-received', { detail: data }))
    }
  }
  break
```

## 修复效果

### 修复前的问题：
1. 管理员发送消息 → 用户收到 ✅
2. 管理员发送消息 → 咨询师收到 ❌

### 修复后的效果：
1. 管理员发送消息 → 用户收到 ✅
2. 管理员发送消息 → 咨询师也收到 ✅

## 后端配合要求

为了确保修复完全生效，后端需要确保：

1. **handleAdminReplyMessage方法**正确实现：
   ```java
   // 处理管理员代替咨询师发送消息
   private void handleAdminReplyMessage(WebSocketSession session, Map<String, Object> message) {
       // 发送给用户
       sendMessageToUser(userId, message);
       
       // 转发给咨询师（让咨询师知道管理员已回复）
       sendMessageToConsultant(consultantId, message);
   }
   ```

2. **消息类型识别**：
   ```java
   if ("admin_reply".equals(type)) {
       handleAdminReplyMessage(session, message);
   }
   ```

3. **数据库记录**：
   - 消息记录中应该标识这是管理员代替咨询师发送的消息
   - 发送者ID记录为管理员ID，但显示时以咨询师身份显示

## 测试验证

### 测试场景：
1. **管理员发送消息**：
   - 管理员在后台选择一个会话
   - 发送消息给用户
   - 验证用户端能收到消息
   - 验证咨询师端也能收到消息

2. **消息显示**：
   - 用户端：消息显示为咨询师发送
   - 咨询师端：消息显示为管理员代替发送（可选）
   - 管理员端：消息显示为已发送

3. **未读计数**：
   - 验证未读消息计数正确更新
   - 验证已读标记正常工作

### 测试步骤：
1. 管理员登录后台管理系统
2. 进入聊天管理界面
3. 选择一个用户与咨询师的会话
4. 发送测试消息
5. 检查用户端小程序是否收到消息
6. 检查咨询师端小程序是否收到消息
7. 验证消息在各端的显示是否正确

## 注意事项

1. **消息去重**：确保同一条消息不会重复显示
2. **消息顺序**：确保消息按时间顺序正确显示
3. **连接状态**：确保WebSocket连接正常
4. **错误处理**：添加适当的错误处理和重试机制

---

**修复时间**：2025-01-21  
**修复状态**：✅ 前端已完成，需要后端配合验证  
**影响范围**：管理员聊天功能、咨询师消息接收
