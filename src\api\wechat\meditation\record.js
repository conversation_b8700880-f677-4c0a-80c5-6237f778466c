import request from '@/utils/request'

// 查询冥想播放记录列表
export function listMeditationRecord(query) {
  return request({
    url: '/system/meditationRecord/list',
    method: 'get',
    params: query
  })
}

// 导出冥想播放记录列表
export function exportMeditationRecord(query) {
  return request({
    url: '/system/meditationRecord/export',
    method: 'post',
    params: query
  })
}

// 获取冥想播放记录详细信息
export function getMeditationRecord(id) {
  return request({
    url: `/system/meditationRecord/${id}`,
    method: 'get'
  })
}

// 新增冥想播放记录
export function addMeditationRecord(data) {
  return request({
    url: '/system/meditationRecord',
    method: 'post',
    data: data
  })
}

// 修改冥想播放记录
export function updateMeditationRecord(data) {
  return request({
    url: '/system/meditationRecord',
    method: 'put',
    data: data
  })
}

// 删除冥想播放记录
export function delMeditationRecord(ids) {
  return request({
    url: `/system/meditationRecord/${ids}`,
    method: 'delete'
  })
}

// 保存播放记录（小程序端）
export function saveMeditationRecord(data) {
  return request({
    url: '/miniapp/meditation/record/save',
    method: 'post',
    data: data
  })
}

// 获取用户冥想播放记录
export function getUserMeditationRecords(userId, query) {
  return request({
    url: `/system/meditationRecord/user/${userId}`,
    method: 'get',
    params: query
  })
}

// 获取冥想的播放记录
export function getMeditationPlayRecords(meditationId, query) {
  return request({
    url: `/system/meditationRecord/meditation/${meditationId}`,
    method: 'get',
    params: query
  })
}

// 获取音频的播放记录
export function getAudioPlayRecords(audioId, query) {
  return request({
    url: `/system/meditationRecord/audio/${audioId}`,
    method: 'get',
    params: query
  })
}

// 获取用户播放历史
export function getUserPlayHistory(query) {
  return request({
    url: '/miniapp/meditation/record/history',
    method: 'get',
    params: query
  })
}

// 获取用户最近播放记录
export function getRecentPlayRecords(limit = 10) {
  return request({
    url: '/miniapp/meditation/record/recent',
    method: 'get',
    params: { limit }
  })
}

// 获取用户播放统计
export function getUserPlayStatistics(userId) {
  return request({
    url: `/system/meditationRecord/statistics/user/${userId}`,
    method: 'get'
  })
}

// 获取冥想播放统计
export function getMeditationPlayStatistics(meditationId) {
  return request({
    url: `/system/meditationRecord/statistics/meditation/${meditationId}`,
    method: 'get'
  })
}

// 获取音频播放统计
export function getAudioPlayStatistics(audioId) {
  return request({
    url: `/system/meditationRecord/statistics/audio/${audioId}`,
    method: 'get'
  })
}

// 更新播放进度
export function updatePlayProgress(recordId, progress, lastPosition) {
  return request({
    url: `/miniapp/meditation/record/progress/${recordId}`,
    method: 'put',
    data: { progress, lastPosition }
  })
}

// 标记播放完成
export function markPlayCompleted(recordId, moodAfter) {
  return request({
    url: `/miniapp/meditation/record/complete/${recordId}`,
    method: 'put',
    data: { moodAfter }
  })
}

// 获取用户未完成的播放记录
export function getIncompleteRecords(userId) {
  return request({
    url: `/system/meditationRecord/incomplete/${userId}`,
    method: 'get'
  })
}

// 批量删除播放记录
export function batchDelMeditationRecord(ids) {
  return request({
    url: '/system/meditationRecord/batch',
    method: 'delete',
    data: ids
  })
}

// 清空用户播放记录
export function clearUserRecords(userId) {
  return request({
    url: `/system/meditationRecord/clear/${userId}`,
    method: 'delete'
  })
}

// 获取播放记录趋势数据
export function getPlayTrendData(query) {
  return request({
    url: '/system/meditationRecord/trend',
    method: 'get',
    params: query
  })
}

// 获取热门冥想排行
export function getPopularMeditations(query) {
  return request({
    url: '/system/meditationRecord/popular',
    method: 'get',
    params: query
  })
}

// 获取用户冥想偏好分析
export function getUserPreferenceAnalysis(userId) {
  return request({
    url: `/system/meditationRecord/preference/${userId}`,
    method: 'get'
  })
}

// 导出用户播放记录
export function exportUserRecords(userId, query) {
  return request({
    url: `/system/meditationRecord/export/user/${userId}`,
    method: 'post',
    params: query
  })
}
