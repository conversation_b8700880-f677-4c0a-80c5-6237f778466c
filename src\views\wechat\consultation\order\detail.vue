<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>咨询订单详情</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
        </div>
      </template>
      
      <div v-loading="loading">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单ID">{{ orderDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ orderDetail.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{ orderDetail.userName || `用户${orderDetail.userId}` }}</el-descriptions-item>
          <el-descriptions-item label="咨询师">{{ orderDetail.consultantName || `咨询师${orderDetail.consultantId}` }}</el-descriptions-item>
          <el-descriptions-item label="咨询时间">
            {{ parseTime(orderDetail.appointmentTime, '{y}-{m}-{d} {h}:{i}') }}
          </el-descriptions-item>
          <el-descriptions-item label="结束时间">
            {{ parseTime(orderDetail.endTime, '{y}-{m}-{d} {h}:{i}') }}
          </el-descriptions-item>
          <el-descriptions-item label="咨询类型">
            <dict-tag :options="sys_consult_type" :value="orderDetail.consultType" />
          </el-descriptions-item>
          <el-descriptions-item label="咨询时长">{{ orderDetail.duration }}分钟</el-descriptions-item>
          <el-descriptions-item label="订单金额">
            <span style="color: #f56c6c; font-weight: bold;">¥{{ orderDetail.totalAmount }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="支付金额">
            <span style="color: #67c23a; font-weight: bold;">¥{{ orderDetail.paymentAmount || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(orderDetail.status)">
              {{ getStatusText(orderDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="getPaymentStatusType(orderDetail.paymentStatus)">
              {{ getPaymentStatusText(orderDetail.paymentStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付方式">
            <dict-tag :options="sys_payment_method" :value="orderDetail.paymentMethod" />
          </el-descriptions-item>
          <el-descriptions-item label="支付时间">
            {{ parseTime(orderDetail.paymentTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="第三方交易号">{{ orderDetail.transactionId }}</el-descriptions-item>
          <el-descriptions-item label="退款金额">
            <span v-if="orderDetail.refundAmount" style="color: #e6a23c; font-weight: bold;">
              ¥{{ orderDetail.refundAmount }}
            </span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="退款时间">
            {{ parseTime(orderDetail.refundTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="取消时间">
            {{ parseTime(orderDetail.cancelTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ parseTime(orderDetail.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ parseTime(orderDetail.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ orderDetail.remark || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="取消原因" :span="2" v-if="orderDetail.cancelReason">
            {{ orderDetail.cancelReason }}
          </el-descriptions-item>
          <el-descriptions-item label="退款原因" :span="2" v-if="orderDetail.refundReason">
            {{ orderDetail.refundReason }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <span>订单操作</span>
      </template>
      
      <el-row :gutter="10">
        <el-col :span="4">
          <el-button type="primary" @click="handleEdit" v-hasPermi="['system:consultantOrder:edit']">
            编辑订单
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="success" @click="handleUpdateStatus" v-hasPermi="['system:consultantOrder:edit']">
            更新状态
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="warning" @click="handleCancel" v-hasPermi="['system:consultantOrder:edit']"
            v-if="orderDetail.status === 'pending' || orderDetail.status === 'paid'">
            取消订单
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="info" @click="handleRefund" v-hasPermi="['system:consultantOrder:edit']"
            v-if="orderDetail.status === 'paid' && orderDetail.paymentAmount > 0">
            申请退款
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="danger" @click="handleDelete" v-hasPermi="['system:consultantOrder:remove']">
            删除订单
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 相关咨询记录 -->
    <el-card class="box-card" style="margin-top: 20px;" v-if="consultationRecords.length > 0">
      <template #header>
        <span>相关咨询记录</span>
      </template>
      
      <el-table :data="consultationRecords" style="width: 100%">
        <el-table-column prop="id" label="记录ID" width="80" />
        <el-table-column prop="actualStartTime" label="开始时间" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.actualStartTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column prop="actualEndTime" label="结束时间" width="180">
          <template #default="scope">
            {{ parseTime(scope.row.actualEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="时长" width="100">
          <template #default="scope">
            {{ scope.row.duration }}分钟
          </template>
        </el-table-column>
        <el-table-column prop="consultContent" label="咨询内容" show-overflow-tooltip />
        <el-table-column prop="userRating" label="用户评分" width="100">
          <template #default="scope">
            <el-rate v-model="scope.row.userRating" disabled show-score />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button link type="primary" @click="viewConsultationDetail(scope.row)">
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup name="ConsultantOrderDetail">
import { 
  getConsultantOrderDetails, 
  updateConsultantOrderStatus,
  cancelConsultantOrder,
  refundConsultantOrder,
  delConsultantOrder
} from "@/api/wechat/consultation/order";

const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const { sys_consult_type, sys_payment_method } = proxy.useDict("sys_consult_type", "sys_payment_method");

const loading = ref(true);
const orderDetail = ref({});
const consultationRecords = ref([]);

/** 获取订单详情 */
function getOrderDetail() {
  const orderId = route.params.id;
  loading.value = true;
  
  getConsultantOrderDetails(orderId).then(response => {
    orderDetail.value = response.data;
    consultationRecords.value = response.data.consultationRecords || [];
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 返回列表 */
function goBack() {
  router.go(-1);
}

/** 编辑订单 */
function handleEdit() {
  router.push(`/wechat/consultation/order/edit/${orderDetail.value.id}`);
}

/** 更新状态 */
function handleUpdateStatus() {
  proxy.$prompt('请输入新的订单状态', '更新状态', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValidator: (value) => {
      if (!value) {
        return '状态不能为空';
      }
      return true;
    }
  }).then(({ value }) => {
    updateConsultantOrderStatus(orderDetail.value.id, value).then(() => {
      proxy.$modal.msgSuccess("状态更新成功");
      getOrderDetail();
    });
  }).catch(() => { });
}

/** 取消订单 */
function handleCancel() {
  proxy.$prompt('请输入取消原因', '取消订单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValidator: (value) => {
      if (!value) {
        return '取消原因不能为空';
      }
      return true;
    }
  }).then(({ value }) => {
    cancelConsultantOrder(orderDetail.value.id, value).then(() => {
      proxy.$modal.msgSuccess("订单取消成功");
      getOrderDetail();
    });
  }).catch(() => { });
}

/** 退款订单 */
function handleRefund() {
  proxy.$prompt('请输入退款金额', '退款订单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'number',
    inputValidator: (value) => {
      if (!value || value <= 0) {
        return '退款金额必须大于0';
      }
      if (value > orderDetail.value.paymentAmount) {
        return '退款金额不能超过支付金额';
      }
      return true;
    }
  }).then(({ value }) => {
    proxy.$prompt('请输入退款原因', '退款原因', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValidator: (reason) => {
        if (!reason) {
          return '退款原因不能为空';
        }
        return true;
      }
    }).then(({ value: reason }) => {
      refundConsultantOrder(orderDetail.value.id, parseFloat(value), reason).then(() => {
        proxy.$modal.msgSuccess("退款成功");
        getOrderDetail();
      });
    });
  }).catch(() => { });
}

/** 删除订单 */
function handleDelete() {
  proxy.$modal.confirm('是否确认删除该咨询订单？').then(function () {
    return delConsultantOrder(orderDetail.value.id);
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功");
    router.push('/wechat/consultation/order');
  }).catch(() => { });
}

/** 查看咨询记录详情 */
function viewConsultationDetail(record) {
  router.push(`/wechat/consultation/record/detail/${record.id}`);
}

/** 获取订单状态类型 */
function getStatusType(status) {
  const statusMap = {
    'pending': 'warning',
    'paid': 'success',
    'completed': 'success',
    'cancelled': 'danger',
    'refunded': 'info'
  };
  return statusMap[status] || 'info';
}

/** 获取订单状态文本 */
function getStatusText(status) {
  const statusMap = {
    'pending': '待支付',
    'paid': '已支付',
    'completed': '已完成',
    'cancelled': '已取消',
    'refunded': '已退款'
  };
  return statusMap[status] || status;
}

/** 获取支付状态类型 */
function getPaymentStatusType(status) {
  const statusMap = {
    'unpaid': 'warning',
    'paid': 'success',
    'failed': 'danger',
    'refunded': 'info'
  };
  return statusMap[status] || 'info';
}

/** 获取支付状态文本 */
function getPaymentStatusText(status) {
  const statusMap = {
    'unpaid': '未支付',
    'paid': '已支付',
    'failed': '支付失败',
    'refunded': '已退款'
  };
  return statusMap[status] || status;
}

onMounted(() => {
  getOrderDetail();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  margin-bottom: 20px;
}
</style>
