<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="日期范围" prop="dateRange">
        <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="YYYY-MM-DD" />
      </el-form-item>
      <el-form-item label="门店" prop="centerId">
        <el-select v-model="queryParams.centerId" placeholder="请选择门店" clearable style="width: 200px">
          <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:systemTimeSlot:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:systemTimeSlot:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:systemTimeSlot:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Setting" @click="showGenerateDialog"
          v-hasPermi="['system:systemTimeSlot:generate']">生成时间槽</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Refresh" @click="showRegenerateDialog"
          v-hasPermi="['system:systemTimeSlot:regenerate']">重新生成</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" @click="showCleanDialog"
          v-hasPermi="['system:systemTimeSlot:clean']">清理过期</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button-group>
          <el-button :type="viewMode === 'list' ? 'primary' : 'default'" icon="List"
            @click="switchViewMode('list')">列表</el-button>
          <el-button :type="viewMode === 'grid' ? 'primary' : 'default'" icon="Grid"
            @click="switchViewMode('grid')">网格</el-button>
          <el-button :type="viewMode === 'stats' ? 'primary' : 'default'" icon="DataAnalysis"
            @click="switchViewMode('stats')">统计</el-button>
        </el-button-group>
      </el-col> -->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="loadData"></right-toolbar>
    </el-row>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="mb20" v-if="viewMode !== 'list'">
      <el-col :span="6">
        <el-statistic title="总时间槽" :value="statistics.total" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="可用" :value="statistics.available" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="已预约" :value="statistics.booked" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="不可用" :value="statistics.unavailable" />
      </el-col>
    </el-row>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'">
      <el-table v-loading="loading" :data="systemTimeSlotList" @selection-change="handleSelectionChange"
        style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" width="80" />
        <el-table-column label="门店" align="center" width="120">
          <template #default="scope">
            {{ getStoreName(scope.row.centerId) }}
          </template>
        </el-table-column>
        <el-table-column label="日期" align="center" prop="dateKey" min-width="100" />
        <el-table-column label="星期" align="center" prop="weekDay" min-width="80" />
        <el-table-column label="时间段" align="center" width="100">
          <template #default="scope">
            {{ scope.row.timeRange?.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="开始时间" align="center" prop="startTime" min-width="100" />
        <el-table-column label="结束时间" align="center" prop="endTime" min-width="100" />
        <el-table-column label="可用咨询师" align="center" prop="availableCounselors" min-width="100" />
        <el-table-column label="总咨询师" align="center" prop="totalCounselors" min-width="100" />
        <!-- <el-table-column label="是否可用" align="center" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.hasAvailable ? 'success' : 'danger'">
              {{ scope.row.hasAvailable ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column> -->
        <el-table-column label="状态" align="center" width="80">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" min-width="200">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:systemTimeSlot:edit']">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:systemTimeSlot:remove']">删除</el-button>
            <el-button link type="info" icon="View" @click="viewSlotDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="loadData" />
    </div>

    <!-- 网格视图 -->
    <div v-else-if="viewMode === 'grid'" class="grid-container">
      <!-- 加载提示 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="loading-icon">
          <Loading />
        </el-icon>
        <p>正在加载网格数据...</p>
      </div>

      <!-- 网格内容 -->
      <div v-else>
        <!-- 日期选择器 -->
        <div class="date-selector">
          <el-date-picker v-model="selectedGridDate" type="date" placeholder="选择日期" @change="handleGridDateChange"
            style="margin-bottom: 20px;" />
          <span style="margin-left: 10px; color: #666;">
            当前显示：{{ formatSelectedDate(selectedGridDate) }}
          </span>
        </div>

        <!-- 时间网格 -->
        <div v-if="selectedGridDate && currentDateSlots.length > 0" class="time-grid">
          <!-- 表头：时间段 -->
          <div class="grid-header">
            <div class="time-label">时间段</div>
            <div class="counselor-header">可用咨询师</div>
            <div class="counselor-header">总咨询师</div>
            <div class="counselor-header">状态</div>
            <div class="counselor-header">操作</div>
          </div>

          <!-- 时间行 -->
          <div v-for="slot in currentDateSlots" :key="slot.id" class="time-row">
            <div class="time-label">{{ slot.startTime }}-{{ slot.endTime }}</div>
            <div class="slot-cell">{{ slot.availableCounselors }}</div>
            <div class="slot-cell">{{ slot.totalCounselors }}</div>
            <div class="slot-cell">
              <el-tag :type="getStatusType(slot.status)">
                {{ getStatusText(slot.status) }}
              </el-tag>
            </div>
            <div class="slot-cell">
              <el-button size="small" type="primary" @click="viewSlotDetail(slot)">详情</el-button>
            </div>
          </div>
        </div>

        <!-- 无数据提示 -->
        <div v-else-if="selectedGridDate && currentDateSlots.length === 0" class="no-data">
          <el-empty description="该日期没有系统时间槽数据" />
        </div>

        <!-- 选择日期提示 -->
        <div v-else class="select-date-tip">
          <el-empty description="请选择日期查看系统时间槽网格" />
        </div>
      </div>
    </div>

    <!-- 统计视图 -->
    <div v-else-if="viewMode === 'stats'" class="stats-container">
      <!-- 图表区域 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>按日期统计</span>
            </template>
            <div ref="dateChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>状态分布</span>
            </template>
            <div ref="statusChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mb20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>可用性趋势</span>
            </template>
            <div ref="availabilityChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>时间段分布</span>
            </template>
            <div ref="timeRangeChart" style="height: 300px;"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细数据表格 -->
      <el-card shadow="hover">
        <template #header>
          <span>详细统计数据</span>
        </template>
        <el-table :data="detailStats" border>
          <el-table-column label="日期" prop="date" />
          <el-table-column label="总时间槽" prop="total" />
          <el-table-column label="可用" prop="available" />
          <el-table-column label="已预约" prop="booked" />
          <el-table-column label="不可用" prop="unavailable" />
          <el-table-column label="利用率">
            <template #default="scope">
              {{ scope.row.total > 0 ? (scope.row.booked / scope.row.total * 100).toFixed(1) : 0 }}%
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 系统时间槽详情对话框 -->
    <el-dialog title="系统时间槽详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border v-if="currentSlot">
        <el-descriptions-item label="ID">{{ currentSlot.id }}</el-descriptions-item>
        <el-descriptions-item label="日期">{{ currentSlot.slotDate }}</el-descriptions-item>
        <el-descriptions-item label="时间段">{{ currentSlot.timeRange?.name || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ currentSlot.startTime }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ currentSlot.endTime }}</el-descriptions-item>
        <el-descriptions-item label="可用咨询师数">{{ currentSlot.availableCounselors }}</el-descriptions-item>
        <el-descriptions-item label="总咨询师数">{{ currentSlot.totalCounselors }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentSlot.status)">
            {{ getStatusText(currentSlot.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="门店">{{ getStoreName(currentSlot.centerId) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ currentSlot.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentSlot.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改系统时间槽对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="systemTimeSlotRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="门店" prop="centerId">
              <el-select v-model="form.centerId" placeholder="请选择门店" style="width: 100%">
                <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日期" prop="dateKey">
              <el-date-picker v-model="form.dateKey" type="date" placeholder="选择日期" value-format="YYYY-MM-DD"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="时间段" prop="rangeId">
              <el-select v-model="form.rangeId" placeholder="请选择时间段" style="width: 100%">
                <el-option v-for="range in timeRangeList" :key="range.id" :label="range.name" :value="range.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="星期" prop="weekDay">
              <el-input v-model="form.weekDay" placeholder="请输入星期" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker v-model="form.startTime" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="选择开始时间"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker v-model="form.endTime" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="选择结束时间"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="可用咨询师数" prop="availableCounselors">
              <el-input-number v-model="form.availableCounselors" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总咨询师数" prop="totalCounselors">
              <el-input-number v-model="form.totalCounselors" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="12">
            <el-form-item label="是否可用" prop="hasAvailable">
              <el-switch v-model="form.hasAvailable" />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="可用" :value="0" />
                <el-option label="已预约" :value="1" />
                <el-option label="不可用" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 生成系统时间槽对话框 -->
    <el-dialog title="生成系统时间槽" v-model="generateOpen" width="500px" append-to-body>
      <el-form ref="generateRef" :model="generateForm" label-width="80px">
        <el-form-item label="日期范围" prop="dateRange">
          <el-date-picker v-model="generateForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" value-format="YYYY-MM-DD"
            @change="(val) => { generateForm.startDate = val?.[0]; generateForm.endDate = val?.[1]; }" />
        </el-form-item>
        <el-form-item label="门店" prop="centerId">
          <el-select v-model="generateForm.centerId" placeholder="请选择门店" style="width: 100%">
            <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleGenerate">确 定</el-button>
          <el-button @click="generateOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 重新生成系统时间槽对话框 -->
    <el-dialog title="重新生成系统时间槽" v-model="regenerateOpen" width="500px" append-to-body>
      <el-form ref="regenerateRef" :model="regenerateForm" label-width="80px">
        <el-form-item label="日期范围" prop="dateRange">
          <el-date-picker v-model="regenerateForm.dateRange" type="daterange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD"
            @change="(val) => { regenerateForm.startDate = val?.[0]; regenerateForm.endDate = val?.[1]; }" />
        </el-form-item>
        <el-form-item label="门店" prop="centerId">
          <el-select v-model="regenerateForm.centerId" placeholder="请选择门店" style="width: 100%">
            <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleRegenerate">确 定</el-button>
          <el-button @click="regenerateOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 清理过期时间槽对话框 -->
    <el-dialog title="清理过期系统时间槽" v-model="cleanOpen" width="500px" append-to-body>
      <el-form ref="cleanRef" :model="cleanForm" label-width="80px">
        <el-form-item label="清理日期" prop="beforeDate">
          <el-date-picker v-model="cleanForm.beforeDate" type="date" placeholder="选择清理日期" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="门店" prop="centerId">
          <el-select v-model="cleanForm.centerId" placeholder="请选择门店" style="width: 100%">
            <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
          </el-select>
        </el-form-item>
        <el-alert title="警告" type="warning" description="此操作将删除指定日期之前的所有过期系统时间槽，请谨慎操作！" show-icon :closable="false" />
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="danger" @click="handleClean">确 定</el-button>
          <el-button @click="cleanOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SystemTimeSlot">
import {
  listSystemTimeSlot, getSystemTimeSlot, delSystemTimeSlot, addSystemTimeSlot, updateSystemTimeSlot,
  generateSystemTimeSlots, regenerateSystemTimeSlots, cleanExpiredSlots, updateAvailabilityStats,
  getSlotsByDate, getSlotsByDateRange, getAvailableSlots, getFormattedTimeSlots
} from "@/api/wechat/systemTimeSlot";
import { listActiveTimeRanges } from "@/api/wechat/timeRange";
import { listStore } from "@/api/wechat/store";
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, getCurrentInstance, computed, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import { Loading } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance();

// 数据变量
const systemTimeSlotList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 时间段列表
const timeRangeList = ref([]);
// 门店列表
const storeList = ref([]);
// 生成对话框
const generateOpen = ref(false);
// 重新生成对话框
const regenerateOpen = ref(false);
// 清理对话框
const cleanOpen = ref(false);
// 视图模式
const viewMode = ref('list'); // list, grid, stats
// 网格数据
const gridData = ref([]);
// 网格选中日期
const selectedGridDate = ref('');
// 详情对话框
const detailOpen = ref(false);
const currentSlot = ref(null);
// 图表引用
const dateChart = ref(null);
const statusChart = ref(null);
const availabilityChart = ref(null);
const timeRangeChart = ref(null);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  dateRange: null,
  centerId: 1
});

// 表单参数
const form = reactive({
  id: null,
  centerId: 1,
  dateKey: null,
  weekDay: null,
  rangeId: null,
  startTime: null,
  endTime: null,
  availableCounselors: 0,
  totalCounselors: 0,
  hasAvailable: true,
  status: 0,
  delFlag: 0
});

// 生成表单
const generateForm = reactive({
  startDate: null,
  endDate: null,
  centerId: 1
});

// 重新生成表单
const regenerateForm = reactive({
  startDate: null,
  endDate: null,
  centerId: 1
});

// 清理表单
const cleanForm = reactive({
  beforeDate: null,
  centerId: 1
});

// 表单校验
const rules = reactive({
  centerId: [
    { required: true, message: "门店不能为空", trigger: "change" }
  ],
  dateKey: [
    { required: true, message: "日期不能为空", trigger: "blur" }
  ],
  rangeId: [
    { required: true, message: "时间段不能为空", trigger: "change" }
  ],
  startTime: [
    { required: true, message: "开始时间不能为空", trigger: "blur" }
  ],
  endTime: [
    { required: true, message: "结束时间不能为空", trigger: "blur" }
  ]
});

// 统计信息
const statistics = computed(() => {
  const stats = { total: 0, available: 0, booked: 0, unavailable: 0 };
  const data = viewMode.value === 'list' ? systemTimeSlotList.value : gridData.value;
  data.forEach(slot => {
    stats.total++;
    switch (slot.status) {
      case 0: stats.available++; break;
      case 1: stats.booked++; break;
      case 2: stats.unavailable++; break;
    }
  });
  return stats;
});

// 当前日期的时间槽数据
const currentDateSlots = computed(() => {
  if (!selectedGridDate.value || !gridData.value) return [];
  return gridData.value.filter(slot => slot.slotDate === selectedGridDate.value);
});

// 详细统计数据
const detailStats = computed(() => {
  const statsMap = new Map();
  const data = viewMode.value === 'list' ? systemTimeSlotList.value : gridData.value;

  data.forEach(slot => {
    const date = slot.slotDate;

    if (!statsMap.has(date)) {
      statsMap.set(date, {
        date,
        total: 0,
        available: 0,
        booked: 0,
        unavailable: 0
      });
    }

    const stats = statsMap.get(date);
    stats.total++;
    switch (slot.status) {
      case 0: stats.available++; break;
      case 1: stats.booked++; break;
      case 2: stats.unavailable++; break;
    }
  });

  return Array.from(statsMap.values()).sort((a, b) => a.date.localeCompare(b.date));
});

/** 获取状态类型 */
function getStatusType(status) {
  switch (status) {
    case 0: return 'success';
    case 1: return 'warning';
    case 2: return 'danger';
    default: return 'info';
  }
}

/** 获取状态文本 */
function getStatusText(status) {
  switch (status) {
    case 0: return '可用';
    case 1: return '已预约';
    case 2: return '不可用';
    default: return '未知';
  }
}

/** 查询系统时间槽列表 */
function getList() {
  loading.value = true;
  const params = { ...queryParams };
  if (params.dateRange && params.dateRange.length === 2) {
    params.startDate = params.dateRange[0];
    params.endDate = params.dateRange[1];
    delete params.dateRange;
  }

  listSystemTimeSlot(params).then(response => {
    systemTimeSlotList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 加载数据 */
function loadData() {
  loading.value = true;

  if (viewMode.value === 'list') {
    // 列表视图：分页加载
    getList();
  } else if (viewMode.value === 'grid') {
    // 网格视图：按日期加载
    if (selectedGridDate.value) {
      loadGridData();
    } else {
      // 设置默认日期为今天
      selectedGridDate.value = new Date().toISOString().split('T')[0];
      loadGridData();
    }
  } else if (viewMode.value === 'stats') {
    // 统计视图：加载所有数据
    const params = {
      pageNum: 1,
      pageSize: 10000,
      centerId: queryParams.centerId
    };

    listSystemTimeSlot(params).then(response => {
      gridData.value = response.rows || [];
      loading.value = false;

      nextTick(() => {
        initCharts();
      });
    }).catch(() => {
      loading.value = false;
    });
  }
}

/** 加载网格数据 */
function loadGridData() {
  if (!selectedGridDate.value) return;

  loading.value = true;

  getSlotsByDate(selectedGridDate.value, queryParams.centerId).then(response => {
    gridData.value = response.data || [];
    console.log(`加载了 ${gridData.value.length} 条系统时间槽数据`);
    loading.value = false;
  }).catch(error => {
    console.error('加载网格数据失败:', error);
    ElMessage.error('加载数据失败');
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  loadData();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 切换视图模式 */
function switchViewMode(mode) {
  console.log('切换视图模式:', mode);
  viewMode.value = mode;

  // 添加延迟以避免UI阻塞
  setTimeout(() => {
    loadData();
  }, 100);
}

/** 格式化选中的日期 */
function formatSelectedDate(date) {
  if (!date) return '请选择日期';

  // 如果是Date对象，转换为字符串
  if (date instanceof Date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 如果已经是字符串格式，直接返回
  if (typeof date === 'string') {
    return date;
  }

  return '请选择日期';
}

/** 网格日期变化处理 */
function handleGridDateChange(date) {
  // 确保日期格式正确
  if (date instanceof Date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    selectedGridDate.value = `${year}-${month}-${day}`;
  } else {
    selectedGridDate.value = date;
  }

  if (selectedGridDate.value) {
    loadGridData();
  }
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加系统时间槽";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getSystemTimeSlot(id).then(response => {
    Object.assign(form, response.data);
    open.value = true;
    title.value = "修改系统时间槽";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["systemTimeSlotRef"].validate(valid => {
    if (valid) {
      if (form.id != null) {
        updateSystemTimeSlot(form).then(response => {
          ElMessage.success("修改成功");
          open.value = false;
          loadData();
        });
      } else {
        addSystemTimeSlot(form).then(response => {
          ElMessage.success("新增成功");
          open.value = false;
          loadData();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const slotIds = row.id ? [row.id] : ids.value;
  ElMessageBox.confirm('是否确认删除系统时间槽编号为"' + slotIds + '"的数据项？').then(function () {
    return delSystemTimeSlot(slotIds);
  }).then(() => {
    loadData();
    ElMessage.success("删除成功");
  }).catch(() => { });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.id = null;
  form.centerId = 1;
  form.dateKey = null;
  form.weekDay = null;
  form.rangeId = null;
  form.startTime = null;
  form.endTime = null;
  form.availableCounselors = 0;
  form.totalCounselors = 0;
  form.hasAvailable = true;
  form.status = 0;
  form.delFlag = 0;
  proxy.resetForm("systemTimeSlotRef");
}

/** 显示生成对话框 */
function showGenerateDialog() {
  generateOpen.value = true;
}

/** 显示重新生成对话框 */
function showRegenerateDialog() {
  regenerateOpen.value = true;
}

/** 显示清理对话框 */
function showCleanDialog() {
  cleanOpen.value = true;
}

/** 生成系统时间槽 */
function handleGenerate() {
  if (!generateForm.startDate || !generateForm.endDate) {
    ElMessage.error('请选择日期范围');
    return;
  }

  generateSystemTimeSlots(generateForm.startDate, generateForm.endDate, generateForm.centerId).then(response => {
    ElMessage.success(response.msg);
    generateOpen.value = false;
    loadData();
  });
}

/** 重新生成系统时间槽 */
function handleRegenerate() {
  if (!regenerateForm.startDate || !regenerateForm.endDate) {
    ElMessage.error('请选择日期范围');
    return;
  }

  regenerateSystemTimeSlots(regenerateForm.startDate, regenerateForm.endDate, regenerateForm.centerId).then(response => {
    ElMessage.success(response.msg);
    regenerateOpen.value = false;
    loadData();
  });
}

/** 清理过期时间槽 */
function handleClean() {
  if (!cleanForm.beforeDate) {
    ElMessage.error('请选择清理日期');
    return;
  }

  cleanExpiredSlots(cleanForm.beforeDate, cleanForm.centerId).then(response => {
    ElMessage.success(response.msg);
    cleanOpen.value = false;
    loadData();
  });
}

/** 查看时间槽详情 */
function viewSlotDetail(slot) {
  currentSlot.value = slot;
  detailOpen.value = true;
}

/** 获取时间段列表 */
function getTimeRangeList() {
  listActiveTimeRanges().then(response => {
    timeRangeList.value = response.rows || response.data || [];
  });
}

/** 初始化图表 */
function initCharts() {
  // 延迟一点时间确保DOM和数据都准备好了
  setTimeout(() => {
    initDateChart();
    initStatusChart();
    initAvailabilityChart();
    initTimeRangeChart();
  }, 100);
}

/** 初始化日期统计图表 */
function initDateChart() {
  if (!dateChart.value) return;
  const chart = echarts.init(dateChart.value);

  // 按日期统计
  const dateStats = {};
  gridData.value.forEach(slot => {
    const date = slot.slotDate;
    if (!dateStats[date]) {
      dateStats[date] = { available: 0, booked: 0, unavailable: 0 };
    }
    switch (slot.status) {
      case 0: dateStats[date].available++; break;
      case 1: dateStats[date].booked++; break;
      case 2: dateStats[date].unavailable++; break;
    }
  });

  const dates = Object.keys(dateStats).sort();
  const availableData = dates.map(date => dateStats[date].available);
  const bookedData = dates.map(date => dateStats[date].booked);
  const unavailableData = dates.map(date => dateStats[date].unavailable);

  const option = {
    tooltip: { trigger: 'axis' },
    legend: { data: ['可用', '已预约', '不可用'] },
    xAxis: { type: 'category', data: dates },
    yAxis: { type: 'value' },
    series: [
      { name: '可用', type: 'bar', data: availableData, color: '#67c23a' },
      { name: '已预约', type: 'bar', data: bookedData, color: '#e6a23c' },
      { name: '不可用', type: 'bar', data: unavailableData, color: '#f56c6c' }
    ]
  };

  chart.setOption(option);
}

/** 初始化状态统计图表 */
function initStatusChart() {
  if (!statusChart.value) return;
  const chart = echarts.init(statusChart.value);

  const data = [
    { name: '可用', value: statistics.value.available, itemStyle: { color: '#67c23a' } },
    { name: '已预约', value: statistics.value.booked, itemStyle: { color: '#e6a23c' } },
    { name: '不可用', value: statistics.value.unavailable, itemStyle: { color: '#f56c6c' } }
  ];

  const option = {
    tooltip: { trigger: 'item' },
    series: [{
      name: '状态分布',
      type: 'pie',
      radius: ['40%', '70%'],
      data: data
    }]
  };

  chart.setOption(option);
}

/** 初始化可用性趋势图表 */
function initAvailabilityChart() {
  if (!availabilityChart.value) return;
  const chart = echarts.init(availabilityChart.value);

  // 按日期计算可用性比例
  const dateStats = {};
  gridData.value.forEach(slot => {
    const date = slot.slotDate;
    if (!dateStats[date]) {
      dateStats[date] = { total: 0, available: 0 };
    }
    dateStats[date].total++;
    if (slot.status === 0) {
      dateStats[date].available++;
    }
  });

  const dates = Object.keys(dateStats).sort();
  const availabilityData = dates.map(date => {
    const stats = dateStats[date];
    return stats.total > 0 ? (stats.available / stats.total * 100).toFixed(1) : 0;
  });

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: '{b}: {c}%'
    },
    xAxis: { type: 'category', data: dates },
    yAxis: {
      type: 'value',
      axisLabel: { formatter: '{value}%' }
    },
    series: [{
      name: '可用性',
      type: 'line',
      data: availabilityData,
      smooth: true,
      itemStyle: { color: '#409eff' }
    }]
  };

  chart.setOption(option);
}

/** 初始化时间段统计图表 */
function initTimeRangeChart() {
  if (!timeRangeChart.value) return;
  const chart = echarts.init(timeRangeChart.value);

  // 按时间段统计
  const timeRangeStats = {};
  gridData.value.forEach(slot => {
    const timeRange = slot.timeRange?.name || '未知';
    timeRangeStats[timeRange] = (timeRangeStats[timeRange] || 0) + 1;
  });

  const data = Object.entries(timeRangeStats).map(([name, value]) => ({ name, value }));

  const option = {
    tooltip: { trigger: 'item' },
    series: [{
      name: '时间段分布',
      type: 'pie',
      radius: '50%',
      data: data
    }]
  };

  chart.setOption(option);
}

/** 获取门店列表 */
function getStoreList() {
  listStore().then(response => {
    storeList.value = response.rows || response.data || [];
  });
}

/** 根据门店ID获取门店名称 */
function getStoreName(centerId) {
  const store = storeList.value.find(item => item.id === centerId);
  return store ? store.name : `门店${centerId}`;
}

/** 根据日期计算星期 */
function getWeekDay(dateStr) {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  return weekDays[date.getDay()];
}

// 监听日期变化，自动计算星期
watch(() => form.dateKey, (newDate) => {
  if (newDate) {
    form.weekDay = getWeekDay(newDate);
  }
});

onMounted(() => {
  getTimeRangeList();
  getStoreList();
  loadData();
});
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.grid-container {
  margin-top: 20px;
}

.time-grid {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.grid-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.time-label {
  width: 150px;
  padding: 10px;
  text-align: center;
  font-weight: bold;
  border-right: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.counselor-header {
  flex: 1;
  padding: 10px;
  text-align: center;
  font-weight: bold;
  border-right: 1px solid #e4e7ed;
}

.time-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
}

.time-row:last-child {
  border-bottom: none;
}

.slot-cell {
  flex: 1;
  min-height: 50px;
  border-right: 1px solid #e4e7ed;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.loading-container {
  text-align: center;
  padding: 50px;
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.loading-icon {
  font-size: 24px;
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.date-selector {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.no-data,
.select-date-tip {
  padding: 50px;
  text-align: center;
}

.stats-container {
  margin-top: 20px;
}
</style>
