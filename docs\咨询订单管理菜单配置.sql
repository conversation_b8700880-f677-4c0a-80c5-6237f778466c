-- 咨询订单管理菜单配置SQL脚本

-- 1. 查询咨询系统菜单ID（如果已存在）
-- 如果咨询系统菜单不存在，需要先创建
-- INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
-- VALUES ('咨询系统', 0, 6, 'consultation', '', '', 1, 0, 'M', '0', '0', '', 'message', 'admin', sysdate(), 'admin', sysdate(), '咨询系统菜单');

-- 假设咨询系统菜单ID为 @consultation_menu_id，请根据实际情况替换
SET @consultation_menu_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '咨询系统' AND parent_id = 0);

-- 2. 插入二级菜单：咨询订单管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询订单管理', @consultation_menu_id, 4, 'order', 'wechat/consultation/order/index', '', 1, 0, 'C', '0', '0', 'system:consultantOrder:list', 'shopping-cart', 'admin', sysdate(), 'admin', sysdate(), '咨询订单管理菜单');

SET @order_menu_id = LAST_INSERT_ID();

-- 3. 插入按钮权限：咨询订单查询
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询订单查询', @order_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:query', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 4. 插入按钮权限：咨询订单新增
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询订单新增', @order_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:add', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 5. 插入按钮权限：咨询订单修改
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询订单修改', @order_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:edit', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 6. 插入按钮权限：咨询订单删除
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询订单删除', @order_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:remove', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 7. 插入按钮权限：咨询订单导出
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('咨询订单导出', @order_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:export', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 8. 插入按钮权限：订单状态管理
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('订单状态管理', @order_menu_id, 6, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:status', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 9. 插入按钮权限：订单取消
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('订单取消', @order_menu_id, 7, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:cancel', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 10. 插入按钮权限：订单退款
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('订单退款', @order_menu_id, 8, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:refund', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 11. 插入按钮权限：订单统计
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('订单统计', @order_menu_id, 9, '', '', '', 1, 0, 'F', '0', '0', 'system:consultantOrder:stats', '#', 'admin', sysdate(), 'admin', sysdate(), '');

-- 查询结果验证
SELECT 
    m1.menu_name as '一级菜单',
    m2.menu_name as '二级菜单', 
    m3.menu_name as '按钮权限',
    m3.perms as '权限标识',
    m3.menu_type as '菜单类型'
FROM sys_menu m1
LEFT JOIN sys_menu m2 ON m1.menu_id = m2.parent_id
LEFT JOIN sys_menu m3 ON m2.menu_id = m3.parent_id
WHERE m1.menu_name = '咨询系统' 
    AND m2.menu_name = '咨询订单管理'
ORDER BY m3.order_num;

-- 数据字典配置（如果需要）
-- 订单状态字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('咨询订单状态', 'sys_consultant_order_status', '0', 'admin', sysdate(), '咨询订单状态列表');

SET @dict_type_id = LAST_INSERT_ID();

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '待支付', 'pending', 'sys_consultant_order_status', '', 'warning', 'N', '0', 'admin', sysdate(), '待支付状态'),
(2, '已支付', 'paid', 'sys_consultant_order_status', '', 'success', 'N', '0', 'admin', sysdate(), '已支付状态'),
(3, '已完成', 'completed', 'sys_consultant_order_status', '', 'success', 'N', '0', 'admin', sysdate(), '已完成状态'),
(4, '已取消', 'cancelled', 'sys_consultant_order_status', '', 'danger', 'N', '0', 'admin', sysdate(), '已取消状态'),
(5, '已退款', 'refunded', 'sys_consultant_order_status', '', 'info', 'N', '0', 'admin', sysdate(), '已退款状态');

-- 支付状态字典
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('支付状态', 'sys_payment_status', '0', 'admin', sysdate(), '支付状态列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '未支付', 'unpaid', 'sys_payment_status', '', 'warning', 'N', '0', 'admin', sysdate(), '未支付状态'),
(2, '已支付', 'paid', 'sys_payment_status', '', 'success', 'N', '0', 'admin', sysdate(), '已支付状态'),
(3, '支付失败', 'failed', 'sys_payment_status', '', 'danger', 'N', '0', 'admin', sysdate(), '支付失败状态'),
(4, '已退款', 'refunded', 'sys_payment_status', '', 'info', 'N', '0', 'admin', sysdate(), '已退款状态');

-- 咨询类型字典（如果不存在）
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('咨询类型', 'sys_consult_type', '0', 'admin', sysdate(), '咨询类型列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '在线咨询', 'online', 'sys_consult_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '在线咨询'),
(2, '电话咨询', 'phone', 'sys_consult_type', '', 'success', 'N', '0', 'admin', sysdate(), '电话咨询'),
(3, '面对面咨询', 'offline', 'sys_consult_type', '', 'warning', 'N', '0', 'admin', sysdate(), '面对面咨询');

-- 支付方式字典（如果不存在）
INSERT INTO sys_dict_type (dict_name, dict_type, status, create_by, create_time, remark) 
VALUES ('支付方式', 'sys_payment_method', '0', 'admin', sysdate(), '支付方式列表');

INSERT INTO sys_dict_data (dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, remark) VALUES
(1, '微信支付', 'wechat', 'sys_payment_method', '', 'success', 'Y', '0', 'admin', sysdate(), '微信支付'),
(2, '支付宝', 'alipay', 'sys_payment_method', '', 'primary', 'N', '0', 'admin', sysdate(), '支付宝'),
(3, '银行卡', 'bank', 'sys_payment_method', '', 'info', 'N', '0', 'admin', sysdate(), '银行卡支付'),
(4, '现金', 'cash', 'sys_payment_method', '', 'warning', 'N', '0', 'admin', sysdate(), '现金支付');

COMMIT;
