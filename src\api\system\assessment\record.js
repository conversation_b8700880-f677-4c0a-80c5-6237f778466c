import request from '@/utils/request'

// 查询测评记录列表
export function listRecord(query) {
  return request({
    url: '/system/assessment/record/list',
    method: 'get',
    params: query
  })
}

// 查询测评记录详细
export function getRecord(id) {
  return request({
    url: '/system/assessment/record/' + id,
    method: 'get'
  })
}

// 新增测评记录
export function addRecord(data) {
  return request({
    url: '/system/assessment/record',
    method: 'post',
    data: data
  })
}

// 修改测评记录
export function updateRecord(data) {
  return request({
    url: '/system/assessment/record',
    method: 'put',
    data: data
  })
}

// 删除测评记录
export function delRecord(ids) {
  return request({
    url: '/system/assessment/record/' + ids,
    method: 'delete'
  })
}

// 导出测评记录
export function exportRecord(query) {
  return request({
    url: '/system/assessment/record/export',
    method: 'post',
    params: query
  })
}

// 获取测评统计
export function getRecordStats(query) {
  return request({
    url: '/system/assessment/record/stats',
    method: 'get',
    params: query
  })
}

// 获取用户测评记录
export function getUserRecords(userId) {
  return request({
    url: '/system/assessment/record/user/' + userId,
    method: 'get'
  })
}

// 获取量表测评记录
export function getScaleRecords(scaleId) {
  return request({
    url: '/system/assessment/record/scale/' + scaleId,
    method: 'get'
  })
}

// 重新计算测评结果
export function recalculateResult(id) {
  return request({
    url: '/system/assessment/record/recalculate/' + id,
    method: 'put'
  })
}

// // 获取测评统计
// export function getRecordStats(query) {
//   return request({
//     url: '/system/assessment/record/stats',
//     method: 'get',
//     params: query
//   })
// }

// 查看测评答案
export function getRecordAnswers(id) {
  return request({
    url: '/system/assessment/record/' + id + '/answers',
    method: 'get'
  })
}

// 生成测评报告
export function generateRecordReport(id, reportLevel = 1) {
  return request({
    url: '/system/assessment/record/' + id + '/report',
    method: 'post',
    data: { reportLevel }
  })
}
