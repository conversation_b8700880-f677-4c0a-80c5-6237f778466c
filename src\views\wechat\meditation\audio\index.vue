<template>
  <div class="app-container">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="mb-4" separator="/">
      <el-breadcrumb-item :to="{ path: '/wechat/meditation/meditation' }">冥想管理</el-breadcrumb-item>
      <el-breadcrumb-item>音频管理</el-breadcrumb-item>
      <el-breadcrumb-item>{{ meditationInfo.title }}</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 冥想信息卡片 -->
    <div class="meditation-info-card">
      <div class="card-header">
        <span>冥想信息</span>
        <el-button type="primary" @click="goBack">返回冥想列表</el-button>
      </div>
      <el-row :gutter="20">
        <el-col :span="4">
          <image-preview :src="meditationInfo.coverImage" :width="100" :height="100" v-if="meditationInfo.coverImage" />
        </el-col>
        <el-col :span="20">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="冥想名称">{{ meditationInfo.title }}</el-descriptions-item>
            <el-descriptions-item label="总时长">{{ meditationInfo.totalDuration }}秒</el-descriptions-item>
            <el-descriptions-item label="音频数量">{{ meditationInfo.audioCount }}</el-descriptions-item>
            <el-descriptions-item label="是否有试听">
              <el-tag :type="meditationInfo.hasTrial ? 'success' : 'info'">
                {{ meditationInfo.hasTrial ? '是' : '否' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="播放次数">{{ meditationInfo.playCount }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <dict-tag :options="sys_course_status" :value="meditationInfo.status" />
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="音频名称" prop="audioName">
        <el-input v-model="queryParams.audioName" placeholder="请输入音频名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="是否试听" prop="isTrial">
        <el-select v-model="queryParams.isTrial" placeholder="请选择" clearable style="width: 120px">
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
          <el-option label="正常" :value="1" />
          <el-option label="停用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:meditation:audio:add']">新增音频</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:meditation:audio:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:meditation:audio:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:meditation:audio:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="handleBatchOrder"
          v-hasPermi="['system:meditation:audio:edit']">批量排序</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 音频列表表格 -->
    <el-table v-loading="loading" :data="audioList" @selection-change="handleSelectionChange" row-key="id">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="排序" width="80" align="center">
        <template #default="scope">
          <el-button-group>
            <el-button size="small" icon="ArrowUp" @click="moveAudio(scope.row.id, 'up')" :disabled="scope.$index === 0"
              v-hasPermi="['system:meditation:audio:edit']" />
            <el-button size="small" icon="ArrowDown" @click="moveAudio(scope.row.id, 'down')"
              :disabled="scope.$index === audioList.length - 1" v-hasPermi="['system:meditation:audio:edit']" />
          </el-button-group>
        </template>
      </el-table-column>
      <el-table-column label="序号" align="center" prop="orderNum" width="80" />
      <el-table-column label="封面" align="center" prop="coverImage" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.coverImage" :width="50" :height="50" v-if="scope.row.coverImage" />
        </template>
      </el-table-column>
      <el-table-column label="音频名称" align="center" prop="audioName" :show-overflow-tooltip="true" />
      <el-table-column label="时长" align="center" prop="duration" width="100">
        <template #default="scope">
          <span>{{ formatDuration(scope.row.duration) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试听" align="center" prop="isTrial" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.isTrial ? 'success' : 'info'" size="small">
            {{ scope.row.isTrial ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="试听时长" align="center" prop="trialDuration" width="100">
        <template #default="scope">
          <span v-if="scope.row.isTrial">{{ scope.row.trialDuration }}秒</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="文件大小" align="center" prop="fileSize" width="100">
        <template #default="scope">
          <span>{{ formatFileSize(scope.row.fileSize) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="播放次数" align="center" prop="playCount" width="100" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"
            @change="handleStatusChange(scope.row)" v-hasPermi="['system:meditation:audio:edit']" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:meditation:audio:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:meditation:audio:edit']">修改</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:meditation:audio:remove']">删除</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="play" icon="VideoPlay">播放</el-dropdown-item>
                <el-dropdown-item command="copy" icon="CopyDocument">复制</el-dropdown-item>
                <el-dropdown-item command="statistics" icon="DataAnalysis">统计</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改音频对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="audioRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="音频名称" prop="audioName">
              <el-input v-model="form.audioName" placeholder="请输入音频名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序号" prop="orderNum">
              <el-input-number v-model="form.orderNum" :min="1" :max="999" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="音频描述" prop="audioDescription">
          <el-input v-model="form.audioDescription" type="textarea" placeholder="请输入音频描述" />
        </el-form-item>
        <el-form-item label="音频封面" prop="coverImage">
          <image-upload v-model="form.coverImage" />
        </el-form-item>
        <el-form-item label="音频文件" prop="audioUrl">
          <file-upload v-model="form.audioUrl" :file-type="['mp3', 'wav', 'flac']"
            @file-uploaded="handleFileUploaded" />
          <div v-if="audioAnalyzing" class="audio-analyzing">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
            <span class="ml-2">正在分析音频文件...</span>
          </div>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="时长(秒)" prop="duration">
              <el-input-number v-model="form.duration" :min="1" :max="99999" :disabled="audioAnalyzing"
                placeholder="上传音频后自动获取" />
              <div class="form-item-tip" v-if="form.duration">
                时长：{{ formatDuration(form.duration) }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否试听" prop="isTrial">
              <el-switch v-model="form.isTrial" :active-value="1" :inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.isTrial">
          <el-col :span="12">
            <el-form-item label="试听时长(秒)" prop="trialDuration">
              <el-input-number v-model="form.trialDuration" :min="10" :max="300" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="文件格式" prop="fileFormat">
              <el-input v-model="form.fileFormat" placeholder="上传音频后自动获取" :disabled="audioAnalyzing" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="比特率(kbps)" prop="bitrate">
              <el-input-number v-model="form.bitrate" :min="64" :max="320" :disabled="audioAnalyzing"
                placeholder="上传音频后自动获取" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="文件大小" prop="fileSize">
              <el-input v-model="fileSizeDisplay" placeholder="上传音频后自动获取" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采样率(Hz)" prop="sampleRate">
              <el-input-number v-model="form.sampleRate" :min="8000" :max="192000" :disabled="audioAnalyzing"
                placeholder="上传音频后自动获取" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 音频详情对话框 -->
    <el-dialog title="音频详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="音频名称">{{ detailForm.audioName }}</el-descriptions-item>
        <el-descriptions-item label="排序号">{{ detailForm.orderNum }}</el-descriptions-item>
        <el-descriptions-item label="时长">{{ formatDuration(detailForm.duration) }}</el-descriptions-item>
        <el-descriptions-item label="是否试听">
          <el-tag :type="detailForm.isTrial ? 'success' : 'info'">
            {{ detailForm.isTrial ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="试听时长" v-if="detailForm.isTrial">{{ detailForm.trialDuration
        }}秒</el-descriptions-item>
        <el-descriptions-item label="文件大小">{{ formatFileSize(detailForm.fileSize) }}</el-descriptions-item>
        <el-descriptions-item label="文件格式">{{ detailForm.fileFormat?.toUpperCase() }}</el-descriptions-item>
        <el-descriptions-item label="比特率">{{ detailForm.bitrate }}kbps</el-descriptions-item>
        <el-descriptions-item label="播放次数">{{ detailForm.playCount }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="sys_course_status" :value="detailForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailForm.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="音频封面" :span="2">
          <image-preview :src="detailForm.coverImage" :width="200" :height="150" v-if="detailForm.coverImage" />
        </el-descriptions-item>
        <el-descriptions-item label="音频描述" :span="2">{{ detailForm.audioDescription }}</el-descriptions-item>
        <el-descriptions-item label="音频文件" :span="2">
          <audio controls v-if="detailForm.audioUrl" style="width: 100%">
            <source :src="detailForm.audioUrl" :type="`audio/${detailForm.fileFormat}`">
            您的浏览器不支持音频播放。
          </audio>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 批量排序对话框 -->
    <el-dialog title="批量排序" v-model="sortOpen" width="600px" append-to-body>
      <div class="sort-container">
        <p class="mb-4">拖拽下方音频项目来调整顺序：</p>
        <draggable v-model="sortList" item-key="id" @end="onSortEnd">
          <template #item="{ element, index }">
            <div class="sort-item">
              <el-icon class="drag-handle">
                <Rank />
              </el-icon>
              <span class="order-num">{{ index + 1 }}</span>
              <span class="audio-name">{{ element.audioName }}</span>
              <span class="duration">{{ formatDuration(element.duration) }}</span>
            </div>
          </template>
        </draggable>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSort">确 定</el-button>
          <el-button @click="sortOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MeditationAudio">
import {
  listMeditationAudio, getMeditationAudio, delMeditationAudio, addMeditationAudio,
  updateMeditationAudio, exportMeditationAudio, updateMeditationAudioStatus,
  moveMeditationAudio, batchUpdateAudioOrder
} from "@/api/wechat/meditation/audio";
import { getMeditationWithAudios } from "@/api/wechat/meditation/meditation";
import draggable from 'vuedraggable';
import { getCurrentInstance, ref, reactive, onMounted, computed, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";

const { proxy } = getCurrentInstance();
const { sys_course_status } = proxy.useDict('sys_course_status');

const route = useRoute();
const router = useRouter();

const audioList = ref([]);
const meditationInfo = ref({});
const open = ref(false);
const detailOpen = ref(false);
const sortOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const sortList = ref([]);
const audioAnalyzing = ref(false);
const fileSizeDisplay = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    meditationId: route.params.id,
    audioName: null,
    isTrial: null,
    status: null
  },
  rules: {
    audioName: [
      { required: true, message: "音频名称不能为空", trigger: "blur" }
    ],
    audioUrl: [
      { required: true, message: "音频文件不能为空", trigger: "blur" }
    ],
    duration: [
      { required: true, message: "时长不能为空", trigger: "blur" }
    ],
    orderNum: [
      { required: true, message: "排序号不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, rules } = toRefs(data);

/** 查询音频列表 */
function getList() {
  loading.value = true;
  listMeditationAudio(queryParams.value.meditationId, queryParams.value).then(response => {
    audioList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取冥想信息 */
function getMeditationInfo() {
  getMeditationWithAudios(route.params.id).then(response => {
    meditationInfo.value = response.data;
  });
}

/** 返回冥想列表 */
function goBack() {
  router.push('/wechat/group-order-classification/meditation-system/meditation');
}

/** 格式化时长 */
function formatDuration(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/** 格式化文件大小 */
function formatFileSize(bytes) {
  if (!bytes || bytes <= 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(unitIndex === 0 ? 0 : 2)} ${units[unitIndex]}`;
}



/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    meditationId: route.params.id,
    audioName: null,
    audioDescription: null,
    audioUrl: null,
    coverImage: null,
    duration: null,
    orderNum: null,
    isTrial: 0,
    trialDuration: 60,
    fileSize: null,
    fileFormat: null,
    bitrate: null,
    sampleRate: null,
    status: 1,
    remark: null
  };
  fileSizeDisplay.value = "";
  audioAnalyzing.value = false;
  proxy.resetForm("audioRef");
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加音频";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const audioId = row.id || ids.value;
  getMeditationAudio(audioId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改音频";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["audioRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateMeditationAudio(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMeditationAudio(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const audioIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除音频编号为"' + audioIds + '"的数据项？').then(function () {
    return delMeditationAudio(audioIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/meditation/audio/export', {
    ...queryParams.value
  }, `meditation_audio_${new Date().getTime()}.xlsx`)
}

/** 批量排序操作 */
function handleBatchOrder() {
  sortList.value = [...audioList.value];
  sortOpen.value = true;
}

/** 状态修改 */
function handleStatusChange(row) {
  updateMeditationAudioStatus(row.id, row.status).then(() => {
    proxy.$modal.msgSuccess("修改成功");
  }).catch(() => {
    row.status = row.status === 0 ? 1 : 0;
  });
}

/** 移动音频顺序 */
function moveAudio(id, direction) {
  moveMeditationAudio(id, direction).then(() => {
    proxy.$modal.msgSuccess("移动成功");
    getList();
  });
}

/** 详情操作 */
function handleDetail(row) {
  const audioId = row.id;
  getMeditationAudio(audioId).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 排序结束事件 */
function onSortEnd() {
  // 拖拽结束后更新排序号
  sortList.value.forEach((item, index) => {
    item.orderNum = index + 1;
  });
}

/** 提交排序 */
function submitSort() {
  const orderData = sortList.value.map(item => ({
    id: item.id,
    orderNum: item.orderNum
  }));

  batchUpdateAudioOrder(orderData).then(() => {
    proxy.$modal.msgSuccess("排序成功");
    sortOpen.value = false;
    getList();
  });
}



/** 获取音频时长 */
function getAudioDuration(fileUrl) {
  return new Promise((resolve) => {
    const audio = new Audio();
    audio.crossOrigin = "anonymous";

    audio.onloadedmetadata = () => {
      if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
        resolve(audio.duration);
      } else {
        resolve(0);
      }
    };

    audio.onerror = () => {
      console.warn('无法加载音频文件获取时长');
      resolve(0);
    };

    // 设置超时
    setTimeout(() => {
      console.warn('获取音频时长超时');
      resolve(0);
    }, 5000);

    audio.src = fileUrl;
  });
}



/** 设置默认音频信息 */
function setDefaultAudioInfo(fileFormat) {
  // 根据文件格式设置默认比特率
  const defaultBitrates = {
    'mp3': 128,
    'wav': 1411, // CD质量
    'flac': 1000 // 无损压缩
  };

  const defaultSampleRates = {
    'mp3': 44100,
    'wav': 44100,
    'flac': 44100
  };

  if (!form.value.bitrate) {
    form.value.bitrate = defaultBitrates[fileFormat] || 128;
  }

  if (!form.value.sampleRate) {
    form.value.sampleRate = defaultSampleRates[fileFormat] || 44100;
  }
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case "play":
      // 播放音频
      if (row.audioUrl) {
        const audio = new Audio(row.audioUrl);
        audio.play().catch(e => {
          proxy.$modal.msgError("音频播放失败：" + e.message);
        });
      }
      break;
    case "copy":
      // 复制音频到其他冥想
      proxy.$modal.msgInfo("复制功能开发中...");
      break;
    case "statistics":
      // 查看音频统计
      proxy.$modal.msgInfo("统计功能开发中...");
      break;
  }
}

/** 处理文件上传成功 */
function handleFileUploaded(fileInfo) {
  console.log('文件上传成功:', fileInfo);

  if (!fileInfo) return;

  audioAnalyzing.value = true;

  // 设置文件信息
  if (fileInfo.size) {
    form.value.fileSize = fileInfo.size;
    fileSizeDisplay.value = formatFileSize(fileInfo.size);
  }

  // 从文件名获取格式
  if (fileInfo.originalName) {
    const fileExtension = fileInfo.originalName.split('.').pop().toLowerCase();
    form.value.fileFormat = fileExtension;

    // 设置默认音频参数
    setDefaultAudioInfo(fileExtension);
  }

  // 尝试获取音频时长
  if (fileInfo.url) {
    getAudioDuration(fileInfo.url).then(duration => {
      if (duration > 0) {
        form.value.duration = Math.round(duration);
      }
    }).catch(error => {
      console.warn('获取音频时长失败:', error);
    }).finally(() => {
      audioAnalyzing.value = false;
    });
  } else {
    audioAnalyzing.value = false;
  }
}

onMounted(() => {
  getMeditationInfo();
  getList();
});
</script>

<style scoped>
.meditation-info-card {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background: #f8f9fa;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sort-container {
  max-height: 400px;
  overflow-y: auto;
}

.sort-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  cursor: move;
  transition: all 0.3s;
}

.sort-item:hover {
  background: #e6f7ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.drag-handle {
  margin-right: 12px;
  color: #909399;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.order-num {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #409eff;
  color: white;
  border-radius: 50%;
  margin-right: 12px;
  font-weight: bold;
}

.audio-name {
  flex: 1;
  font-weight: 500;
  color: #303133;
}

.duration {
  color: #909399;
  font-size: 14px;
}

.audio-analyzing {
  display: flex;
  align-items: center;
  margin-top: 8px;
  color: #409eff;
  font-size: 14px;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}
</style>
