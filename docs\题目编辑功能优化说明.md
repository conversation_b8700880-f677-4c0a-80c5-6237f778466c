# 题目编辑功能优化说明

## 优化概述

根据后端提供的带选项信息的题目详情接口，优化了题目编辑功能，使用 `getQuestionDetails` 接口替代原来的分别获取题目和选项信息的方式。

## 优化背景

### 原有实现方式
在编辑题目时，需要进行两次API调用：
1. 调用 `getQuestion(id)` 获取题目基本信息
2. 调用 `getOptionsByQuestion(id)` 获取题目选项信息

### 优化后实现方式
使用后端提供的 `getQuestionDetails(id)` 接口，一次性获取题目和选项的完整信息。

## 后端接口说明

```java
/**
 * 获取题目详情（包含选项信息）
 */
@PreAuthorize("@ss.hasPermi('system:question:query')")
@GetMapping(value = "/details/{id}")
public AjaxResult getDetails(@PathVariable("id") Long id) {
    return success(questionService.selectQuestionWithOptions(id));
}
```

- **接口路径**：`GET /system/question/details/{id}`
- **权限要求**：`system:question:query`
- **返回数据**：包含题目基本信息和选项列表的完整数据

## 前端优化内容

### 1. API接口文件更新

在 `src/api/system/assessment/question.js` 中已添加：

```javascript
// 获取题目详情（包含选项信息）
export function getQuestionDetails(id) {
  return request({
    url: '/system/question/details/' + id,
    method: 'get'
  })
}
```

### 2. 题目管理页面优化

**文件**：`src/views/system/assessment/question/index.vue`

#### 修改前：
```javascript
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getQuestion(_id).then(response => {
    form.value = response.data;
    // 获取选项
    getOptionsByQuestion(_id).then(res => {
      form.value.options = res.data || [];
    });
    open.value = true;
    title.value = "修改题目";
  });
}
```

#### 修改后：
```javascript
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  // 使用带选项信息的接口
  getQuestionDetails(_id).then(response => {
    form.value = response.data;
    // 选项信息已经包含在响应中
    if (response.data.options) {
      form.value.options = response.data.options;
    } else {
      form.value.options = [];
    }
    open.value = true;
    title.value = "修改题目";
  });
}
```

#### 导入语句更新：
```javascript
// 添加 getQuestionDetails 到导入列表
import { 
  listQuestion, 
  getQuestion, 
  getQuestionDetails,  // 新增
  delQuestion, 
  addQuestion, 
  updateQuestion, 
  exportQuestion, 
  getQuestionsByScale, 
  updateQuestionOrder 
} from "@/api/system/assessment/question";
```

### 3. 量表管理页面优化

**文件**：`src/views/system/assessment/scale/index.vue`

#### 修改前：
```javascript
/** 修改题目按钮操作 */
function handleUpdateQuestion(row) {
  resetQuestionForm();
  const questionId = row?.id || questionIds.value[0];
  getQuestion(questionId).then(response => {
    Object.assign(questionForm.value, response.data);
    // 确保选项列表存在
    if (!questionForm.value.optionList) {
      questionForm.value.optionList = [];
    }
    questionFormOpen.value = true;
    questionTitle.value = "修改题目";
  });
}
```

#### 修改后：
```javascript
/** 修改题目按钮操作 */
function handleUpdateQuestion(row) {
  resetQuestionForm();
  const questionId = row?.id || questionIds.value[0];
  // 使用带选项信息的接口
  getQuestionDetails(questionId).then(response => {
    Object.assign(questionForm.value, response.data);
    // 选项信息已经包含在响应中
    if (response.data.options) {
      questionForm.value.optionList = response.data.options;
    } else if (!questionForm.value.optionList) {
      questionForm.value.optionList = [];
    }
    questionFormOpen.value = true;
    questionTitle.value = "修改题目";
  });
}
```

#### 导入语句更新：
```javascript
// 添加 getQuestionDetails 到导入列表
import { 
  listQuestion, 
  getQuestion, 
  getQuestionDetails,  // 新增
  delQuestion, 
  addQuestion, 
  updateQuestion 
} from "@/api/system/assessment/question";
```

## 优化效果

### 1. 性能提升
- **减少API调用次数**：从2次减少到1次
- **减少网络请求**：降低网络延迟和服务器负载
- **提升用户体验**：编辑页面加载更快

### 2. 代码简化
- **逻辑更清晰**：一次性获取完整数据
- **错误处理更简单**：只需处理一个API调用的错误
- **维护性更好**：减少了异步调用的复杂性

### 3. 数据一致性
- **避免数据不同步**：确保题目和选项数据的一致性
- **减少竞态条件**：避免多个异步请求可能导致的数据混乱

## 数据结构说明

### 预期的响应数据结构：
```javascript
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "scaleId": 1,
    "questionNo": 1,
    "questionText": "题目内容",
    "questionType": "SINGLE",
    "isRequired": 1,
    "subscaleRef": "子量表",
    "options": [  // 选项列表
      {
        "id": 1,
        "questionId": 1,
        "optionValue": "A",
        "optionText": "选项A",
        "score": 1,
        "orderNum": 1
      },
      {
        "id": 2,
        "questionId": 1,
        "optionValue": "B",
        "optionText": "选项B",
        "score": 2,
        "orderNum": 2
      }
    ]
  }
}
```

## 注意事项

1. **后端数据结构**：确保后端返回的数据结构中包含 `options` 字段
2. **选项字段映射**：注意前端使用的字段名（如 `optionList`）与后端返回的字段名（如 `options`）的映射
3. **空数据处理**：当题目没有选项时，确保返回空数组而不是 null
4. **权限控制**：使用相同的权限标识 `system:question:query`

## 测试建议

1. **功能测试**：
   - 测试编辑有选项的题目
   - 测试编辑无选项的题目
   - 测试编辑不同类型的题目

2. **性能测试**：
   - 对比优化前后的加载时间
   - 测试网络请求数量的减少

3. **兼容性测试**：
   - 确保新接口与现有功能兼容
   - 测试错误处理是否正常

---

**优化时间**：2025-01-21  
**优化状态**：✅ 已完成  
**影响范围**：题目管理页面、量表管理页面中的题目编辑功能
