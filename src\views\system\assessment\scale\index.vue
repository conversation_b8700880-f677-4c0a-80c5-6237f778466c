<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="量表名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入量表名称" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="量表编码" prop="code">
        <el-input v-model="queryParams.code" placeholder="请输入量表编码" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择分类" clearable style="width: 240px">
          <el-option v-for="category in categoryOptions" :key="category.categoryId" :label="category.categoryName"
            :value="category.categoryId" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option v-for="dict in test_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:assessment:scale:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:assessment:scale:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:assessment:scale:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:assessment:scale:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scaleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="量表名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="量表编码" align="center" prop="code" width="100" />
      <el-table-column label="分类" align="center" prop="categoryName" width="100">
        <template #default="scope">
          {{ getCategoryName(scope.row.categoryId) }}
        </template>
      </el-table-column>
      <el-table-column label="题目数量" align="center" prop="questionCount" width="80" />
      <el-table-column label="测评时长" align="center" prop="duration" width="80" />
      <el-table-column label="难度等级" align="center" prop="difficultyDesc" width="80" />
      <el-table-column label="价格" align="center" prop="priceDesc" width="80" />
      <el-table-column label="测试次数" align="center" prop="testCount" width="80" />
      <el-table-column label="评分" align="center" prop="ratingAvg" width="80">
        <template #default="scope">
          <el-rate v-model="scope.row.ratingAvg" disabled show-score text-color="#ff9900" score-template="{value}" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="statusDesc" width="80" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:assessment:scale:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:assessment:scale:edit']">修改</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-if="scope.row.status === 0 && checkPermi(['system:assessment:scale:edit'])"
                  command="publish" icon="Check">发布</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status === 1 && checkPermi(['system:assessment:scale:edit'])"
                  command="offline" icon="Close">下架</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:question:list'])" command="questions"
                  icon="List">题目管理</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:scale:query'])" command="stats"
                  icon="DataAnalysis">统计分析</el-dropdown-item>
                <el-dropdown-item v-if="checkPermi(['system:assessment:scale:remove'])" command="delete"
                  icon="Delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改量表对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="scaleRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="量表名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入量表名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="量表编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入量表编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%">
                <el-option v-for="category in categoryOptions" :key="category.categoryId" :label="category.categoryName"
                  :value="category.categoryId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测评时长" prop="duration">
              <el-input v-model="form.duration" placeholder="请输入测评时长，如：5~10分钟" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="计分类型" prop="scoringType">
              <el-select v-model="form.scoringType" placeholder="请选择计分类型">
                <el-option label="李克特量表" value="LIKERT" />
                <el-option label="求和计分" value="SUM" />
                <el-option label="平均计分" value="AVERAGE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用年龄" prop="applicableAge">
              <el-input v-model="form.applicableAge" placeholder="请输入适用年龄，如：成人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="付费模式" prop="payMode">
              <el-radio-group v-model="form.payMode">
                <el-radio :value="0">免费</el-radio>
                <el-radio :value="1">付费</el-radio>
                <el-radio :value="2">部分付费</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.payMode !== 0">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="form.price" :min="0" :precision="2" :step="0.01" placeholder="请输入价格" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="量表描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入量表描述" />
        </el-form-item>
        <el-form-item label="测评说明" prop="introduction">
          <el-input v-model="form.introduction" type="textarea" placeholder="请输入测评说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 题目管理对话框 -->
    <el-dialog title="题目管理" v-model="questionDialogOpen" width="1200px" append-to-body>
      <div class="question-management">
        <!-- 搜索区域 -->
        <el-form :model="questionQueryParams" ref="questionQueryRef" :inline="true" v-show="questionShowSearch"
          label-width="68px">
          <el-form-item label="题目内容" prop="content">
            <el-input v-model="questionQueryParams.content" placeholder="请输入题目内容" clearable style="width: 240px"
              @keyup.enter="handleQuestionQuery" />
          </el-form-item>
          <el-form-item label="题目类型" prop="questionType">
            <el-select v-model="questionQueryParams.questionType" placeholder="请选择题目类型" clearable style="width: 240px">
              <el-option v-for="dict in question_type_options" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="子量表" prop="subscaleRef">
            <el-input v-model="questionQueryParams.subscaleRef" placeholder="请输入子量表" clearable style="width: 240px"
              @keyup.enter="handleQuestionQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuestionQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuestionQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 操作按钮 -->
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAddQuestion"
              v-hasPermi="['system:question:add']">新增题目</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="questionSingle" @click="handleUpdateQuestion"
              v-hasPermi="['system:question:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="questionMultiple" @click="handleDeleteQuestion"
              v-hasPermi="['system:question:remove']">删除</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="questionShowSearch" @queryTable="getQuestionList"></right-toolbar>
        </el-row>

        <!-- 题目列表 -->
        <el-table v-loading="questionLoading" :data="questionList" @selection-change="handleQuestionSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="ID" align="center" prop="id" width="80" />
          <el-table-column label="题号" align="center" prop="questionNo" width="80" />
          <el-table-column label="题目内容" align="center" :show-overflow-tooltip="true" min-width="200">
            <template #default="scope">
              {{ scope.row.content || scope.row.questionText }}
            </template>
          </el-table-column>
          <el-table-column label="题目类型" align="center" width="100">
            <template #default="scope">
              <dict-tag :options="question_type_options" :value="scope.row.questionType" />
            </template>
          </el-table-column>
          <el-table-column label="子量表" align="center" prop="subscaleRef" width="100" />
          <el-table-column label="是否必答" align="center" width="80">
            <template #default="scope">
              <dict-tag :options="yes_no_options" :value="scope.row.isRequired" />
            </template>
          </el-table-column>
          <el-table-column label="反向计分" align="center" width="80">
            <template #default="scope">
              <dict-tag :options="yes_no_options" :value="scope.row.isReverse" />
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" prop="sort" width="80" />
          <el-table-column label="选项数量" align="center" width="80">
            <template #default="scope">
              {{ scope.row.optionList ? scope.row.optionList.length : 0 }}
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.delFlag === '0' ? 'success' : 'danger'">
                {{ scope.row.delFlag === '0' ? '正常' : '已删除' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdateQuestion(scope.row)"
                  v-hasPermi="['system:question:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleDeleteQuestion(scope.row)"
                  v-hasPermi="['system:question:remove']"></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="questionTotal > 0" :total="questionTotal" v-model:page="questionQueryParams.pageNum"
          v-model:limit="questionQueryParams.pageSize" @pagination="getQuestionList" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="questionDialogOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>



    <!-- 选项编辑对话框 -->
    <el-dialog :title="optionTitle" v-model="optionFormOpen" width="600px" append-to-body>
      <el-form ref="optionFormRef" :model="optionForm" :rules="optionRules" label-width="120px">
        <el-form-item label="选项文本" prop="optionText">
          <el-input v-model="optionForm.optionText" placeholder="请输入选项文本" />
        </el-form-item>
        <el-form-item label="选项分值" prop="optionValue">
          <el-input-number v-model="optionForm.optionValue" :min="0" :max="100" placeholder="请输入选项分值"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="optionForm.sort" :min="0" placeholder="请输入排序" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="optionForm.status">
            <el-radio :value="1">正常</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitOptionForm">确 定</el-button>
          <el-button @click="cancelOption">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 题目编辑对话框 -->
    <el-dialog :title="questionTitle" v-model="questionFormOpen" width="1200px" append-to-body>
      <el-form ref="questionFormRef" :model="questionForm" :rules="questionRules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="题号" prop="questionNo">
              <el-input-number v-model="questionForm.questionNo" :min="1" :max="999" placeholder="请输入题号"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="题目类型" prop="questionType">
              <el-select v-model="questionForm.questionType" placeholder="请选择题目类型" style="width: 100%">
                <el-option v-for="dict in question_type_options" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="题目内容" prop="content">
          <el-input v-model="questionForm.content" type="textarea" placeholder="请输入题目内容" :rows="3" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否必答" prop="isRequired">
              <el-radio-group v-model="questionForm.isRequired">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否反向计分" prop="isReverse">
              <el-radio-group v-model="questionForm.isReverse">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="questionForm.sort" :min="1" placeholder="请输入排序" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="questionForm.isReverse === 1">
            <el-form-item label="反向计分值" prop="reverseValue">
              <el-input-number v-model="questionForm.reverseValue" :min="0" placeholder="请输入反向计分值"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="分量表引用" prop="subscaleRef">
          <el-input v-model="questionForm.subscaleRef" placeholder="请输入分量表引用（可选）" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="questionForm.remark" type="textarea" placeholder="请输入备注" :rows="2" />
        </el-form-item>
      </el-form>

      <!-- 选项管理 -->
      <el-divider content-position="left">选项管理</el-divider>
      <div style="margin-bottom: 20px;">
        <el-button type="primary" size="small" icon="Plus" @click="handleAddOption"
          v-hasPermi="['system:assessment:question:edit']">新增选项</el-button>
      </div>
      <el-table :data="questionForm.optionList" style="width: 100%"
        v-if="questionForm.optionList && questionForm.optionList.length > 0">
        <el-table-column label="选项文本" align="center" prop="optionText" />
        <el-table-column label="选项分值" align="center" prop="optionValue" width="100" />
        <el-table-column label="排序" align="center" prop="sort" width="80" />
        <el-table-column label="状态" align="center" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" width="150">
          <template #default="scope">
            {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleEditOption(scope.row)"
              v-hasPermi="['system:assessment:question:edit']">编辑</el-button>
            <el-button link type="danger" size="small" @click="handleDeleteOption(scope.row)"
              v-hasPermi="['system:assessment:question:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-empty v-else description="暂无选项数据" style="margin: 20px 0;">
        <el-button type="primary" @click="handleAddOption"
          v-hasPermi="['system:assessment:question:edit']">新增选项</el-button>
      </el-empty>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelQuestion">取 消</el-button>
          <el-button type="primary" @click="submitQuestionForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 量表详情对话框 -->
    <el-dialog title="量表详情" v-model="detailOpen" width="1000px" append-to-body>
      <el-tabs v-model="activeDetailTab" type="border-card">
        <!-- 基本信息 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="量表名称">{{ detailForm.name }}</el-descriptions-item>
            <el-descriptions-item label="量表编码">{{ detailForm.code }}</el-descriptions-item>
            <el-descriptions-item label="分类">{{ detailForm.categoryName }}</el-descriptions-item>
            <el-descriptions-item label="题目数量">{{ detailForm.questionCount }}</el-descriptions-item>
            <el-descriptions-item label="测评时长">{{ detailForm.duration }}</el-descriptions-item>
            <el-descriptions-item label="计分类型">{{ detailForm.scoringType }}</el-descriptions-item>
            <el-descriptions-item label="适用年龄">{{ detailForm.applicableAge }}</el-descriptions-item>
            <el-descriptions-item label="价格">{{ detailForm.priceDesc }}</el-descriptions-item>
            <el-descriptions-item label="查看次数">{{ detailForm.viewCount }}</el-descriptions-item>
            <el-descriptions-item label="搜索次数">{{ detailForm.searchCount }}</el-descriptions-item>
            <el-descriptions-item label="评分">
              <el-rate v-model="detailForm.ratingAvg" disabled show-score text-color="#ff9900" score-template="{value}"
                v-if="detailForm.ratingAvg" />
              <span v-else>暂无评分</span>
            </el-descriptions-item>
            <el-descriptions-item label="状态">{{ detailForm.statusDesc }}</el-descriptions-item>
            <el-descriptions-item label="创建时间" :span="2">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="量表描述" :span="2">{{ detailForm.description }}</el-descriptions-item>
            <el-descriptions-item label="测评说明" :span="2">
              <div v-html="detailForm.introduction"></div>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>

        <!-- 题目信息 -->
        <el-tab-pane label="题目信息" name="questions">
          <div v-if="detailForm.questions && detailForm.questions.length > 0">
            <div v-for="question in detailForm.questions" :key="question.id" class="question-item">
              <div class="question-header">
                <span class="question-no">{{ question.questionNo }}.</span>
                <span class="question-text">{{ question.questionText }}</span>
                <el-tag size="small" :type="getQuestionTypeTag(question.questionType)">
                  {{ getQuestionTypeText(question.questionType) }}
                </el-tag>
              </div>

              <div v-if="question.options && question.options.length > 0" class="question-options">
                <div v-for="option in question.options" :key="option.id" class="option-item">
                  <span class="option-value">{{ option.optionValue }}.</span>
                  <span class="option-text">{{ option.optionText }}</span>
                  <span class="option-score">({{ option.score }}分)</span>
                </div>
              </div>

              <div v-if="question.dimension" class="question-dimension">
                <el-tag size="small" type="info">维度: {{ question.dimension }}</el-tag>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无题目信息" />
        </el-tab-pane>

        <!-- 结果解释 -->
        <el-tab-pane label="结果解释" name="interpretations"
          v-if="detailForm.interpretations && detailForm.interpretations.length > 0">
          <div class="interpretations-list">
            <div v-for="interpretation in detailForm.interpretations" :key="interpretation.id"
              class="interpretation-item">
              <div class="interpretation-header">
                <span class="level-name">{{ interpretation.levelName }}</span>
                <span class="score-range">{{ interpretation.minScore }} - {{ interpretation.maxScore }}分</span>
                <el-tag v-if="interpretation.dimension" size="small" type="warning">{{ interpretation.dimension
                }}</el-tag>
              </div>
              <div class="interpretation-description">{{ interpretation.levelDescription }}</div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 统计分析对话框 -->
    <el-dialog title="统计分析" v-model="statsOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="量表名称">{{ statsForm.scaleName }}</el-descriptions-item>
        <el-descriptions-item label="测试总次数">{{ statsForm.testCount }}</el-descriptions-item>
        <el-descriptions-item label="查看总次数">{{ statsForm.viewCount }}</el-descriptions-item>
        <el-descriptions-item label="平均评分">
          <el-rate v-model="statsForm.ratingAvg" disabled show-score text-color="#ff9900" score-template="{value}" />
        </el-descriptions-item>
        <el-descriptions-item label="评价数量">{{ statsForm.ratingCount }}</el-descriptions-item>
        <el-descriptions-item label="完成率">{{ statsForm.completionRate }}%</el-descriptions-item>
      </el-descriptions>
      <div class="mt-4">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="测试趋势" name="trend">
            <div ref="trendChartRef" style="width: 100%; height: 300px;"></div>
          </el-tab-pane>
          <el-tab-pane label="得分分布" name="score">
            <div ref="scoreChartRef" style="width: 100%; height: 300px;"></div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Scale">
import { listScale, getScale, delScale, addScale, updateScale, exportScale, publishScale, unpublishScale, getScaleStats } from "@/api/system/assessment/scale";
import { listQuestion, getQuestion, getQuestionDetails, delQuestion, addQuestion, updateQuestion } from "@/api/system/assessment/question";
import { addOption as addOptionApi, updateOption, delOption, getOptionsByQuestion } from "@/api/system/assessment/option";
import { listCategory } from "@/api/wechat/category";
import { checkPermi } from "@/utils/permission";
import * as echarts from 'echarts';

const { proxy } = getCurrentInstance();
const { question_type_options, yes_no_options, test_status } = proxy.useDict('question_type', 'sys_yes_no', 'test_status');

const scaleList = ref([]);
const categoryOptions = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const statsOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const activeTab = ref('trend');
const trendChart = ref(null);
const scoreChart = ref(null);
const activeDetailTab = ref("basic");
const trendChartRef = ref(null);
const scoreChartRef = ref(null);

// 题目管理相关数据
const questionDialogOpen = ref(false);
const questionList = ref([]);
const questionLoading = ref(false);
const questionShowSearch = ref(true);
const questionIds = ref([]);
const questionSingle = ref(true);
const questionMultiple = ref(true);
const questionTotal = ref(0);
const currentScaleId = ref(null);
const questionQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  scaleId: null,
  content: null,
  questionType: null,
  subscaleRef: null
});



// 选项编辑相关数据
const optionFormOpen = ref(false);
const optionTitle = ref("");
const optionForm = ref({
  id: null,
  questionId: null,
  optionText: "",
  optionValue: null,
  sort: 0,
  status: 1,
  delFlag: "0"
});
const optionRules = ref({
  optionText: [
    { required: true, message: "选项文本不能为空", trigger: "blur" }
  ],
  optionValue: [
    { required: true, message: "选项分值不能为空", trigger: "blur" }
  ]
});

// 题目编辑相关数据
const questionFormOpen = ref(false);
const questionTitle = ref("");
const questionForm = ref({
  id: null,
  scaleId: null,
  questionNo: null,
  content: "",
  questionType: "",
  isReverse: 0,
  reverseValue: null,
  subscaleRef: "",
  isRequired: 1,
  sort: 1,
  remark: ""
});

const questionRules = ref({
  questionNo: [
    { required: true, message: "题号不能为空", trigger: "blur" }
  ],
  content: [
    { required: true, message: "题目内容不能为空", trigger: "blur" }
  ],
  questionType: [
    { required: true, message: "题目类型不能为空", trigger: "change" }
  ],
  isRequired: [
    { required: true, message: "是否必答不能为空", trigger: "change" }
  ],
  sort: [
    { required: true, message: "排序不能为空", trigger: "blur" }
  ]
});

const data = reactive({
  form: {},
  detailForm: {},
  statsForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    code: null,
    categoryId: null,
    status: null
  },
  rules: {
    name: [
      { required: true, message: "量表名称不能为空", trigger: "blur" }
    ],
    code: [
      { required: true, message: "量表编码不能为空", trigger: "blur" }
    ],
    categoryId: [
      { required: true, message: "分类不能为空", trigger: "change" }
    ],
    timeLimit: [
      { required: true, message: "时间限制不能为空", trigger: "blur" }
    ],
    difficultyLevel: [
      { required: true, message: "难度等级不能为空", trigger: "change" }
    ],
    isFree: [
      { required: true, message: "是否免费不能为空", trigger: "change" }
    ],
    price: [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (form.value.payMode !== 0 && (value === null || value === undefined)) {
            callback(new Error('价格不能为空'));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  }
});

const { queryParams, form, detailForm, statsForm, rules } = toRefs(data);

/** 查询量表列表 */
function getList() {
  loading.value = true;
  listScale(queryParams.value).then(response => {
    scaleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询分类列表 */
function getCategoryList() {
  listCategory().then(response => {
    // 提取心理测评分类
    const assessmentCategories = [];
    if (response.data && Array.isArray(response.data)) {
      response.data.forEach(category => {
        if (category.categoryName === '测评' && category.children) {
          assessmentCategories.push(...category.children);
        }
      });
    }
    categoryOptions.value = assessmentCategories;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    code: null,
    description: null,
    introduction: null,
    categoryId: null,
    questionCount: 0,
    duration: null,
    scoringType: null,
    applicableAge: null,
    price: 0.00,
    payMode: 0,
    status: 0
  };
  proxy.resetForm("scaleRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加量表";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getScale(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改量表";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getScale(_id).then(response => {
    detailForm.value = response.data;
    activeDetailTab.value = "basic";
    detailOpen.value = true;
  });
}

// /** 获取题目类型标签 */
// function getQuestionTypeTag(type) {
//   const typeMap = {
//     1: 'primary',   // 单选
//     2: 'success',   // 多选
//     3: 'warning',   // 填空
//     4: 'info'       // 其他
//   };
//   return typeMap[type] || 'info';
// }

/** 获取题目类型文本 */
// function getQuestionTypeText(type) {
//   const typeMap = {
//     1: '单选题',
//     2: '多选题',
//     3: '填空题',
//     4: '其他'
//   };
//   return typeMap[type] || '未知';
// }

/** 提交按钮 */
function submitForm() {
  proxy.$refs["scaleRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateScale(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addScale(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除量表编号为"' + _ids + '"的数据项？').then(function () {
    return delScale(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/assessment/scale/export', {
    ...queryParams.value
  }, `scale_${new Date().getTime()}.xlsx`)
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'publish':
      handlePublish(row);
      break;
    case 'offline':
      handleOffline(row);
      break;
    case 'questions':
      handleQuestions(row);
      break;
    case 'stats':
      handleStats(row);
      break;
    case 'delete':
      handleDelete(row);
      break;
  }
}

/** 发布量表 */
function handlePublish(row) {
  proxy.$modal.confirm('是否确认发布量表"' + row.scaleName + '"？').then(function () {
    return publishScale(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发布成功");
  }).catch(() => { });
}

/** 下架量表 */
function handleOffline(row) {
  proxy.$modal.confirm('是否确认下架量表"' + row.scaleName + '"？').then(function () {
    return unpublishScale(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("下架成功");
  }).catch(() => { });
}

/** 题目管理 */
function handleQuestions(row) {
  currentScaleId.value = row.id;
  questionQueryParams.value.scaleId = row.id;
  questionDialogOpen.value = true;
  getQuestionList();
}

/** 查询题目列表 */
function getQuestionList() {
  questionLoading.value = true;
  listQuestion(questionQueryParams.value).then(response => {
    questionList.value = response.rows;
    questionTotal.value = response.total;
    questionLoading.value = false;
  });
}

/** 题目搜索按钮操作 */
function handleQuestionQuery() {
  questionQueryParams.value.pageNum = 1;
  getQuestionList();
}

/** 重置题目搜索 */
function resetQuestionQuery() {
  proxy.resetForm("questionQueryRef");
  questionQueryParams.value.scaleId = currentScaleId.value;
  handleQuestionQuery();
}

/** 题目多选框选中数据 */
function handleQuestionSelectionChange(selection) {
  questionIds.value = selection.map(item => item.id);
  questionSingle.value = selection.length !== 1;
  questionMultiple.value = !selection.length;
}

/** 新增题目按钮操作 */
function handleAddQuestion() {
  resetQuestionForm();
  questionForm.value.scaleId = currentScaleId.value;
  questionFormOpen.value = true;
  questionTitle.value = "添加题目";
}

/** 修改题目按钮操作 */
function handleUpdateQuestion(row) {
  resetQuestionForm();
  const questionId = row?.id || questionIds.value[0];
  // 使用带选项信息的接口
  getQuestionDetails(questionId).then(response => {
    Object.assign(questionForm.value, response.data);
    // 选项信息已经包含在响应中
    if (response.data.options) {
      questionForm.value.optionList = response.data.options;
    } else if (!questionForm.value.optionList) {
      questionForm.value.optionList = [];
    }
    questionFormOpen.value = true;
    questionTitle.value = "修改题目";
  });
}

/** 删除题目按钮操作 */
function handleDeleteQuestion(row) {
  const questionIds = row?.id || questionIds.value;
  proxy.$modal.confirm('是否确认删除题目编号为"' + questionIds + '"的数据项？').then(function () {
    return delQuestion(questionIds);
  }).then(() => {
    getQuestionList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 题目表单重置 */
function resetQuestionForm() {
  questionForm.value = {
    id: null,
    scaleId: null,
    questionNo: null,
    content: "",
    questionType: "",
    isReverse: 0,
    reverseValue: null,
    subscaleRef: "",
    isRequired: 1,
    sort: 1,
    remark: ""
  };
  nextTick(() => {
    proxy.$refs["questionFormRef"]?.resetFields();
  });
}

/** 取消题目编辑 */
function cancelQuestion() {
  questionFormOpen.value = false;
  resetQuestionForm();
}

/** 提交题目表单 */
function submitQuestionForm() {
  proxy.$refs["questionFormRef"].validate(valid => {
    if (valid) {
      if (questionForm.value.id != null) {
        updateQuestion(questionForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          questionFormOpen.value = false;
          getQuestionList();
        });
      } else {
        addQuestion(questionForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          questionFormOpen.value = false;
          getQuestionList();
        });
      }
    }
  });
}



/** 统计分析 */
function handleStats(row) {
  getScaleStats(row.id).then(response => {
    statsForm.value = response.data;
    statsForm.value.scaleName = row.scaleName;
    statsOpen.value = true;

    // 在下一个DOM更新周期后初始化图表
    nextTick(() => {
      initTrendChart();
      initScoreChart();
    });
  });
}

/** 初始化趋势图表 */
function initTrendChart() {
  if (trendChart.value) {
    trendChart.value.dispose();
  }

  trendChart.value = echarts.init(trendChartRef.value);

  // 模拟数据，实际应从后端获取
  const option = {
    title: {
      text: '测试趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['测试次数', '查看次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '测试次数',
        type: 'line',
        data: [10, 15, 20, 25, 22, 30, 35]
      },
      {
        name: '查看次数',
        type: 'line',
        data: [30, 40, 45, 50, 55, 60, 70]
      }
    ]
  };

  trendChart.value.setOption(option);
}

/** 初始化得分分布图表 */
function initScoreChart() {
  if (scoreChart.value) {
    scoreChart.value.dispose();
  }

  scoreChart.value = echarts.init(scoreChartRef.value);

  // 模拟数据，实际应从后端获取
  const option = {
    title: {
      text: '得分分布'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-20', '21-40', '41-60', '61-80', '81-100']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '人数',
        type: 'bar',
        data: [5, 15, 30, 25, 10]
      }
    ]
  };

  scoreChart.value.setOption(option);
}

// 监听标签页切换
watch(activeTab, (newVal) => {
  nextTick(() => {
    if (newVal === 'trend') {
      initTrendChart();
    } else if (newVal === 'score') {
      initScoreChart();
    }
  });
});

// 监听窗口大小变化，重绘图表
window.addEventListener('resize', () => {
  if (trendChart.value) {
    trendChart.value.resize();
  }
  if (scoreChart.value) {
    scoreChart.value.resize();
  }
});



/** 新增选项 */
function handleAddOption() {
  resetOptionForm();
  optionForm.value.questionId = questionForm.value.id;
  optionTitle.value = "新增选项";
  optionFormOpen.value = true;
}

/** 编辑选项 */
function handleEditOption(row) {
  resetOptionForm();
  optionForm.value = { ...row };
  optionTitle.value = "编辑选项";
  optionFormOpen.value = true;
}

/** 删除选项 */
function handleDeleteOption(row) {
  proxy.$modal.confirm('是否确认删除该选项？').then(function () {
    return delOption(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功");
    // 刷新当前题目的选项列表
    refreshCurrentQuestionOptions();
  }).catch(() => { });
}

/** 重置选项表单 */
function resetOptionForm() {
  optionForm.value = {
    id: null,
    questionId: null,
    optionText: "",
    optionValue: null,
    sort: 0,
    status: 1,
    delFlag: "0"
  };
  proxy.resetForm("optionFormRef");
}

/** 取消选项编辑 */
function cancelOption() {
  optionFormOpen.value = false;
  resetOptionForm();
}

/** 提交选项表单 */
function submitOptionForm() {
  proxy.$refs["optionFormRef"].validate(valid => {
    if (valid) {
      if (optionForm.value.id != null) {
        updateOption(optionForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          optionFormOpen.value = false;
          refreshCurrentQuestionOptions();
        });
      } else {
        addOptionApi(optionForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          optionFormOpen.value = false;
          refreshCurrentQuestionOptions();
        });
      }
    }
  });
}

/** 刷新当前题目的选项列表 */
function refreshCurrentQuestionOptions() {
  if (questionForm.value.id) {
    getOptionsByQuestion(questionForm.value.id).then(response => {
      questionForm.value.optionList = response.data || [];
      // 同时更新题目列表中的选项数量
      const questionIndex = questionList.value.findIndex(q => q.id === questionForm.value.id);
      if (questionIndex !== -1) {
        questionList.value[questionIndex].optionList = response.data || [];
      }
    });
  }
}

/** 获取题目类型标签样式 */
function getQuestionTypeTag(questionType) {
  const typeMap = {
    'SINGLE': 'primary',
    'MULTIPLE': 'success',
    'FILL': 'warning',
    'JUDGE': 'info'
  };
  return typeMap[questionType] || 'info';
}

/** 获取题目类型文本 */
function getQuestionTypeText(questionType) {
  const typeMap = {
    'SINGLE': '单选题',
    'MULTIPLE': '多选题',
    'FILL': '填空题',
    'JUDGE': '判断题'
  };
  return typeMap[questionType] || questionType;
}
function getCategoryName(categoryId) {
  const category = categoryOptions.value.find(item => item.categoryId == categoryId);
  return category ? category.categoryName : "-"
}
onMounted(() => {
  getList();
  getCategoryList();
});

onUnmounted(() => {
  window.removeEventListener('resize', () => { });
  if (trendChart.value) {
    trendChart.value.dispose();
  }
  if (scoreChart.value) {
    scoreChart.value.dispose();
  }
});
</script>

<style scoped>
.el-tag+.el-tag {
  margin-left: 10px;
}

.question-item {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.question-no {
  font-weight: bold;
  color: #409eff;
  min-width: 30px;
}

.question-text {
  flex: 1;
  font-weight: 500;
  color: #303133;
}

.question-options {
  margin: 12px 0;
  padding-left: 20px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  padding: 4px 0;
}

.option-value {
  font-weight: bold;
  color: #606266;
  min-width: 20px;
}

.option-text {
  flex: 1;
  color: #606266;
}

.option-score {
  color: #909399;
  font-size: 12px;
}

.question-dimension {
  margin-top: 8px;
}

.interpretations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.interpretation-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.interpretation-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.level-name {
  font-weight: bold;
  color: #409eff;
  font-size: 16px;
}

.score-range {
  color: #67c23a;
  font-weight: 500;
}

.interpretation-description {
  color: #606266;
  line-height: 1.6;
}
</style>
