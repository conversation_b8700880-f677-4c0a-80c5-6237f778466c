<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="咨询师" prop="counselorId">
        <el-select v-model="queryParams.counselorId" placeholder="请选择咨询师" clearable style="width: 240px">
          <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
            :value="counselor.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="dateKey">
        <el-date-picker v-model="queryParams.dateKey" type="date" placeholder="选择日期" clearable style="width: 240px"
          value-format="YYYY-MM-DD" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="状态" clearable style="width: 240px">
          <el-option label="可用" value="0" />
          <el-option label="已预约" value="1" />
          <el-option label="不可用" value="2" />
          <el-option label="已过期" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:timeSlot:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:timeSlot:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:timeSlot:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Setting" @click="showGenerateDialog"
          v-hasPermi="['system:timeSlot:generate']">生成时间槽</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Delete" @click="showCleanDialog"
          v-hasPermi="['system:timeSlot:clean']">清理过期</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button-group>
          <el-button :type="viewMode === 'list' ? 'primary' : 'default'" icon="List"
            @click="switchViewMode('list')">列表</el-button>
          <el-button :type="viewMode === 'grid' ? 'primary' : 'default'" icon="Grid"
            @click="switchViewMode('grid')">网格</el-button>
          <el-button :type="viewMode === 'stats' ? 'primary' : 'default'" icon="DataAnalysis"
            @click="switchViewMode('stats')">统计</el-button>
        </el-button-group>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="loadData"></right-toolbar>
    </el-row>

    <!-- 统计信息 -->
    <el-row :gutter="20" class="mb20" v-if="viewMode !== 'list'">
      <el-col :span="6">
        <el-statistic title="总时间槽" :value="statistics.total" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="可用" :value="statistics.available" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="已预约" :value="statistics.booked" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="不可用" :value="statistics.unavailable" />
      </el-col>
    </el-row>

    <!-- 列表视图 -->
    <div v-if="viewMode === 'list'">
      <el-table v-loading="loading" :data="timeSlotList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" />
        <el-table-column label="咨询师" align="center">
          <template #default="scope">
            {{ getCounselorName(scope.row.counselorId) }}
          </template>
        </el-table-column>
        <el-table-column label="日期" align="center" prop="dateKey" />
        <el-table-column label="时间段" align="center">
          <template #default="scope">
            {{ scope.row.timeRange?.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="开始时间" align="center" prop="startTime" />
        <el-table-column label="结束时间" align="center" prop="endTime" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column label="是否公开" align="center" prop="isPublic">
          <template #default="scope">
            <el-switch v-model="scope.row.isPublic" :active-value="1" :inactive-value="0"
              @change="handlePublicChange(scope.row)" />
          </template>
        </el-table-column> -->
        <el-table-column label="预约信息" align="center" prop="appointmentInfo" :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:timeSlot:edit']">修改</el-button>
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:timeSlot:remove']">删除</el-button>
            <el-button link type="info" icon="View" @click="viewAppointment(scope.row)"
              v-if="scope.row.status === 1">查看预约</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" @pagination="loadData" />
    </div>

    <!-- 网格视图 -->
    <div v-else-if="viewMode === 'grid'" class="grid-container">
      <!-- 加载提示 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <p>正在加载网格数据...</p>
      </div>

      <!-- 网格内容 -->
      <div v-else>
        <!-- 日期选择器 -->
        <div class="date-selector">
          <el-date-picker v-model="selectedGridDate" type="date" placeholder="选择日期" @change="handleGridDateChange"
            style="margin-bottom: 20px;" />
          <span style="margin-left: 10px; color: #666;">
            当前显示：{{ formatSelectedDate(selectedGridDate) }}
          </span>
        </div>

        <!-- 时间网格 -->
        <div v-if="selectedGridDate && currentDateSlots.length > 0" class="time-grid">
          <!-- 表头：咨询师 -->
          <div class="grid-header">
            <div class="time-label">时间</div>
            <div v-for="counselor in activeCounselors" :key="counselor.id" class="counselor-header">
              {{ counselor.name }}
            </div>
          </div>

          <!-- 时间行 -->
          <div v-for="timeSlot in currentTimeSlots" :key="timeSlot" class="time-row">
            <div class="time-label">{{ timeSlot }}</div>
            <div v-for="counselor in activeCounselors" :key="counselor.id" class="slot-cell">
              <div v-for="slot in getSlotByTimeAndCounselor(timeSlot, counselor.id)" :key="slot.id"
                :class="['slot-item', getSlotClass(slot)]" @click="viewSlotDetail(slot)" :title="getSlotTooltip(slot)">
                <div class="slot-time">{{ slot.startTime.substring(0, 5) }}</div>
                <div class="slot-status">{{ getStatusText(slot.status) }}</div>
              </div>
              <div v-if="!getSlotByTimeAndCounselor(timeSlot, counselor.id).length" class="empty-slot">
                -
              </div>
            </div>
          </div>
        </div>

        <!-- 无数据提示 -->
        <div v-else-if="selectedGridDate && currentDateSlots.length === 0" class="no-data">
          <el-empty description="该日期没有时间槽数据" />
        </div>

        <!-- 选择日期提示 -->
        <div v-else class="select-date-tip">
          <el-empty description="请选择日期查看时间槽网格" />
        </div>
      </div>
    </div>

    <!-- 统计视图 -->
    <div v-else-if="viewMode === 'stats'" class="stats-container">
      <!-- 图表区域 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>按日期统计</span>
            </template>
            <div class="chart-container">
              <div v-if="dateChartLoading" class="chart-loading">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <p>正在加载图表...</p>
              </div>
              <div ref="dateChart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>按咨询师统计</span>
            </template>
            <div class="chart-container">
              <div v-if="counselorChartLoading" class="chart-loading">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <p>正在加载图表...</p>
              </div>
              <div ref="counselorChart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="mb20">
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>状态分布</span>
            </template>
            <div class="chart-container">
              <div v-if="statusChartLoading" class="chart-loading">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <p>正在加载图表...</p>
              </div>
              <div ref="statusChart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <template #header>
              <span>时间段分布</span>
            </template>
            <div class="chart-container">
              <div v-if="timeRangeChartLoading" class="chart-loading">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <p>正在加载图表...</p>
              </div>
              <div ref="timeRangeChart" style="height: 300px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 热力图 -->
      <el-row :gutter="20" class="mb20">
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <span>时间槽分布热力图（咨询师 × 时间）</span>
            </template>
            <div class="chart-container">
              <div v-if="heatmapChartLoading" class="chart-loading">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <p>正在加载图表...</p>
              </div>
              <div ref="heatmapChart" style="height: 400px;"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 详细数据表格 -->
      <el-card shadow="hover">
        <template #header>
          <span>详细统计数据</span>
        </template>
        <el-table :data="detailStats" border>
          <el-table-column label="咨询师">
            <template #default="scope">
              {{ getCounselorName(scope.row.counselorId) }}
            </template>
          </el-table-column>
          <el-table-column label="总时间槽" prop="total" />
          <el-table-column label="可用" prop="available" />
          <el-table-column label="已预约" prop="booked" />
          <el-table-column label="不可用" prop="unavailable" />
          <el-table-column label="利用率">
            <template #default="scope">
              {{ (scope.row.booked / scope.row.total * 100).toFixed(1) }}%
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 时间槽详情对话框 -->
    <el-dialog title="时间槽详情" v-model="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border v-if="currentSlot">
        <el-descriptions-item label="ID">{{ currentSlot.id }}</el-descriptions-item>
        <el-descriptions-item label="排班ID">{{ currentSlot.scheduleId }}</el-descriptions-item>
        <el-descriptions-item label="咨询师">{{ getCounselorName(currentSlot.counselorId) }}</el-descriptions-item>
        <el-descriptions-item label="日期">{{ currentSlot.dateKey }}</el-descriptions-item>
        <el-descriptions-item label="星期">{{ getWeekDayText(currentSlot.weekDay) }}</el-descriptions-item>
        <el-descriptions-item label="时间段">{{ currentSlot.timeRange?.name || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ currentSlot.startTime }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ currentSlot.endTime }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentSlot.status)">
            {{ getStatusText(currentSlot.status) }}
          </el-tag>
        </el-descriptions-item>
        <!-- <el-descriptions-item label="是否公开">{{ currentSlot.isPublic ? '是' : '否' }}</el-descriptions-item> -->
        <el-descriptions-item label="门店">{{ getStoreName(currentSlot.centerId) }}</el-descriptions-item>
        <el-descriptions-item label="时间组哈希">{{ currentSlot.timeGroupHash }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ currentSlot.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ currentSlot.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="editSlot(currentSlot)">修改</el-button>
          <el-button @click="detailOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改时间槽对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="timeSlotRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="咨询师" prop="counselorId">
              <el-select v-model="form.counselorId" placeholder="请选择咨询师" style="width: 100%">
                <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
                  :value="counselor.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="时间段" prop="timeRangeId">
              <el-select v-model="form.timeRangeId" placeholder="请选择时间段" style="width: 100%">
                <el-option v-for="range in timeRangeList" :key="range.id" :label="range.name" :value="range.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="日期" prop="dateKey">
              <el-date-picker v-model="form.dateKey" type="date" placeholder="选择日期" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="可用" value="0" />
                <el-option label="已预约" value="1" />
                <el-option label="不可用" value="2" />
                <el-option label="已过期" value="3" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="12">
            <el-form-item label="是否公开" prop="isPublic">
              <el-radio-group v-model="form.isPublic">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="门店" prop="centerId">
              <el-select v-model="form.centerId" placeholder="请选择门店" style="width: 100%">
                <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 生成时间槽对话框 -->
    <el-dialog title="生成时间槽" v-model="generateOpen" width="500px" append-to-body>
      <el-form ref="generateRef" :model="generateForm" :rules="generateRules" label-width="100px">
        <el-form-item label="咨询师" prop="counselorId">
          <el-select v-model="generateForm.counselorId" placeholder="请选择咨询师（留空为所有咨询师）" style="width: 100%" clearable>
            <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
              :value="counselor.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker v-model="generateForm.startDate" type="date" placeholder="选择开始日期" style="width: 100%"
            value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker v-model="generateForm.endDate" type="date" placeholder="选择结束日期" style="width: 100%"
            value-format="YYYY-MM-DD" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleGenerate">确 定</el-button>
          <el-button @click="generateOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 清理过期时间槽对话框 -->
    <el-dialog title="清理过期时间槽" v-model="cleanOpen" width="400px" append-to-body>
      <el-form ref="cleanRef" :model="cleanForm" :rules="cleanRules" label-width="100px">
        <el-form-item label="清理日期前" prop="beforeDate">
          <el-date-picker v-model="cleanForm.beforeDate" type="date" placeholder="选择日期" style="width: 100%"
            value-format="YYYY-MM-DD" />
        </el-form-item>
        <p style="color: #666; font-size: 12px;">将清理此日期之前的所有过期时间槽</p>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="danger" @click="handleClean">确 定</el-button>
          <el-button @click="cleanOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TimeSlot">
import {
  listTimeSlot, getTimeSlot, delTimeSlot, addTimeSlot, updateTimeSlot,
  generateSlots, generateSlotsForAll, cleanExpiredSlots, updateExpiredSlotStatus
} from "@/api/wechat/timeSlot";
import { listActiveTimeRanges } from "@/api/wechat/timeRange";
import { listConsultant } from "@/api/wechat/consultation/consultant";
import { listStore } from "@/api/wechat/store";
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted, onUnmounted, getCurrentInstance, computed, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import { Loading } from '@element-plus/icons-vue'

// 图表实例管理
const chartInstances = reactive({
  dateChart: null,
  counselorChart: null,
  statusChart: null,
  timeRangeChart: null,
  heatmapChart: null
});

// 图表错误处理
function handleChartError(chartName, error) {
  console.error(`${chartName}图表错误:`, error);
  ElMessage.error(`${chartName}图表加载失败`);
}

// 防抖函数
function debounce(fn, delay) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

// 销毁所有图表实例
function disposeAllCharts() {
  Object.keys(chartInstances).forEach(key => {
    if (chartInstances[key]) {
      chartInstances[key].dispose();
      chartInstances[key] = null;
    }
  });
}

// 组件卸载时清理
onUnmounted(() => {
  disposeAllCharts();
  // 移除所有resize监听器
  window.removeEventListener('resize', handleResize);
});

// 处理窗口调整大小
const handleResize = debounce(() => {
  if (viewMode.value === 'stats') {
    Object.values(chartInstances).forEach(chart => {
      chart && chart.resize();
    });
  }
}, 100);

// 监听窗口大小变化
window.addEventListener('resize', handleResize);

// 确保Loading图标已导入
console.log('Loading icon component:', Loading);

const { proxy } = getCurrentInstance();

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
const total = ref(0);
// 时间槽表格数据
const timeSlotList = ref([]);
// 咨询师列表
const counselorList = ref([]);
// 时间段列表
const timeRangeList = ref([]);
// 门店列表
const storeList = ref([]);
// 弹出层标题
const title = ref("");
// 是否显示弹出层
const open = ref(false);
// 生成对话框
const generateOpen = ref(false);
// 清理对话框
const cleanOpen = ref(false);
// 视图模式
const viewMode = ref('list'); // list, grid, stats
// 网格数据
const gridData = ref([]);
// 当前选中日期
const activeDate = ref('');
// 网格选中日期
const selectedGridDate = ref('');
// 详情对话框
const detailOpen = ref(false);
const currentSlot = ref(null);
// 图表引用
const dateChart = ref(null);
const counselorChart = ref(null);
const statusChart = ref(null);
const timeRangeChart = ref(null);
const heatmapChart = ref(null);

// 图表加载状态
const dateChartLoading = ref(false);
const counselorChartLoading = ref(false);
const statusChartLoading = ref(false);
const timeRangeChartLoading = ref(false);
const heatmapChartLoading = ref(false);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  counselorId: undefined,
  dateKey: undefined,
  status: undefined
});

// 表单参数
const form = reactive({
  id: undefined,
  counselorId: undefined,
  timeRangeId: undefined,
  dateKey: undefined,
  status: "0",
  isPublic: 1,
  centerId: 1,
  remark: undefined
});

// 生成表单
const generateForm = reactive({
  counselorId: undefined,
  startDate: undefined,
  endDate: undefined
});

// 清理表单
const cleanForm = reactive({
  beforeDate: undefined
});

// 表单校验
const rules = reactive({
  counselorId: [
    { required: true, message: "咨询师不能为空", trigger: "change" }
  ],
  timeRangeId: [
    { required: true, message: "时间段不能为空", trigger: "change" }
  ],
  dateKey: [
    { required: true, message: "日期不能为空", trigger: "change" }
  ]
});

// 生成表单校验
const generateRules = reactive({
  startDate: [
    { required: true, message: "开始日期不能为空", trigger: "change" }
  ],
  endDate: [
    { required: true, message: "结束日期不能为空", trigger: "change" }
  ]
});

// 清理表单校验
const cleanRules = reactive({
  beforeDate: [
    { required: true, message: "清理日期不能为空", trigger: "change" }
  ]
});

// 统计信息
const statistics = computed(() => {
  const stats = { total: 0, available: 0, booked: 0, unavailable: 0 };
  const data = viewMode.value === 'list' ? timeSlotList.value : gridData.value;
  data.forEach(slot => {
    stats.total++;
    switch (slot.status) {
      case 0: stats.available++; break;
      case 1: stats.booked++; break;
      case 2: stats.unavailable++; break;
    }
  });
  return stats;
});

// 日期列表
const dateList = computed(() => {
  if (!gridData.value || gridData.value.length === 0) return [];
  const dates = [...new Set(gridData.value.map(slot => slot.dateKey).filter(Boolean))];
  return dates.sort();
});

// 时间段列表
const timeSlots = computed(() => {
  if (!gridData.value || gridData.value.length === 0) return [];
  const times = [...new Set(gridData.value.map(slot => {
    if (slot.startTime && typeof slot.startTime === 'string') {
      return slot.startTime.substring(0, 5);
    }
    return null;
  }).filter(Boolean))];
  return times.sort();
});

// 详细统计数据
const detailStats = computed(() => {
  const statsMap = new Map();
  const data = viewMode.value === 'list' ? timeSlotList.value : gridData.value;

  data.forEach(slot => {
    const counselorId = slot.counselorId;
    // 使用 getCounselorName 方法获取正确的咨询师名称
    const counselorName = getCounselorName(counselorId);

    if (!statsMap.has(counselorId)) {
      statsMap.set(counselorId, {
        counselorId,
        counselorName,
        total: 0,
        available: 0,
        booked: 0,
        unavailable: 0
      });
    }

    const stats = statsMap.get(counselorId);
    stats.total++;
    switch (slot.status) {
      case 0: stats.available++; break;
      case 1: stats.booked++; break;
      case 2: stats.unavailable++; break;
    }
  });

  return Array.from(statsMap.values());
});

// 当前日期的时间槽数据
const currentDateSlots = computed(() => {
  if (!selectedGridDate.value || !gridData.value) return [];
  return gridData.value.filter(slot => slot.dateKey === selectedGridDate.value);
});

// 当前日期的时间段列表
const currentTimeSlots = computed(() => {
  if (!currentDateSlots.value.length) return [];
  const times = [...new Set(currentDateSlots.value.map(slot => {
    if (slot.startTime && typeof slot.startTime === 'string') {
      return slot.startTime.substring(0, 5);
    }
    return null;
  }).filter(Boolean))];
  return times.sort();
});

// 当前日期的活跃咨询师列表
const activeCounselors = computed(() => {
  if (!currentDateSlots.value.length) return [];
  const counselorIds = [...new Set(currentDateSlots.value.map(slot => slot.counselorId))];

  // 如果咨询师列表还没加载完成，返回基于ID的临时对象
  if (!counselorList.value.length) {
    return counselorIds.map(id => ({ id, name: `咨询师${id}` }));
  }

  // 返回匹配的咨询师，如果找不到则创建临时对象
  return counselorIds.map(id => {
    const counselor = counselorList.value.find(c => c.id === id);
    return counselor || { id, name: `咨询师${id}` };
  });
});

/** 获取状态类型 */
function getStatusType(status) {
  // 确保状态值为数字类型
  const numStatus = Number(status);
  const statusMap = {
    0: 'success',  // 可用
    1: 'warning',  // 已预约
    2: 'danger',   // 不可用
    3: 'info'      // 已过期
  };
  return statusMap[numStatus] || 'info';
}

/** 获取状态文本 */
function getStatusText(status) {
  // 确保状态值为数字类型
  const numStatus = Number(status);
  const statusMap = {
    0: '可用',
    1: '已预约',
    2: '不可用',
    3: '已过期'
  };
  return statusMap[numStatus] || '未知';
}

/** 查询时间槽列表 */
function getList() {
  loading.value = true;
  listTimeSlot(queryParams).then(response => {
    timeSlotList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 加载数据 */
async function loadData() {
  loading.value = true;

  try {
    if (viewMode.value === 'list') {
      // 列表视图：分页加载
      const params = {
        ...queryParams,
        pageNum: queryParams.pageNum,
        pageSize: queryParams.pageSize
      };

      const response = await listTimeSlot(params);
      timeSlotList.value = response.rows || [];
      total.value = response.total || 0;
    } else if (viewMode.value === 'grid') {
      // 网格视图：只在选择日期时加载该日期的数据
      if (selectedGridDate.value) {
        await loadGridData();
      } else {
        // 设置默认日期为今天
        selectedGridDate.value = new Date().toISOString().split('T')[0];
        await loadGridData();
      }
    } else if (viewMode.value === 'stats') {
      // 统计视图：加载所有数据
      const params = {
        ...queryParams,
        pageNum: 1,
        pageSize: 10000
      };

      const response = await listTimeSlot(params);
      gridData.value = response.rows || [];

      // 确保咨询师列表已加载，然后初始化图表
      if (counselorList.value.length === 0) {
        await getCounselorList();
      }

      // 等待DOM更新
      await nextTick();
      await initCharts();
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
}

/** 加载网格数据 */
async function loadGridData() {
  if (!selectedGridDate.value) return;

  loading.value = true;

  // 创建新的查询参数，包含日期筛选
  const params = {
    pageNum: 1,
    pageSize: 1000, // 单日数据量相对较小
    dateKey: selectedGridDate.value,
    // 保留其他筛选条件
    counselorId: queryParams.counselorId,
    status: queryParams.status
  };

  console.log('加载网格数据，参数:', params);

  const response = await listTimeSlot(params);
  gridData.value = response.rows || [];
  console.log(`加载了 ${gridData.value.length} 条时间槽数据`);
  loading.value = false;
}

/** 获取咨询师列表 */
function getCounselorList() {
  return listConsultant().then(response => {
    counselorList.value = response.rows || response.data || [];
    console.log('咨询师列表加载完成:', counselorList.value);

    // 如果当前是统计视图，刷新图表
    if (viewMode.value === 'stats' && gridData.value.length > 0) {
      refreshCharts();
    }

    return counselorList.value;
  }).catch(error => {
    console.error('获取咨询师列表失败:', error);
    ElMessage.error('获取咨询师列表失败');
    return [];
  });
}

/** 根据咨询师ID获取咨询师姓名 */
function getCounselorName(counselorId) {
  if (!counselorId) return '未知咨询师';
  const counselor = counselorList.value.find(c => c.id === counselorId);
  return counselor ? counselor.name : `咨询师${counselorId}`;
}

/** 格式化选中的日期 */
function formatSelectedDate(date) {
  if (!date) return '请选择日期';

  // 如果是Date对象，转换为字符串
  if (date instanceof Date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 如果已经是字符串格式，直接返回
  if (typeof date === 'string') {
    return date;
  }

  return '请选择日期';
}

/** 获取时间段列表 */
function getTimeRangeList() {
  listActiveTimeRanges().then(response => {
    timeRangeList.value = response.data || [];
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  // 重置表单数据到初始状态
  Object.assign(form, {
    id: undefined,
    counselorId: undefined,
    timeRangeId: undefined,
    dateKey: undefined,
    status: "0",
    isPublic: 1,
    centerId: 1,
    remark: undefined
  });

  // 重置表单验证状态
  if (proxy.$refs["timeSlotRef"]) {
    proxy.$refs["timeSlotRef"].resetFields();
  }
}

/** 搜索按钮操作 */
async function handleQuery() {
  queryParams.pageNum = 1;
  await loadData();

  // 如果是统计视图，等待数据加载完成后再刷新图表
  if (viewMode.value === 'stats') {
    await nextTick();
    await refreshCharts();
  }
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 切换视图模式 */
async function switchViewMode(mode) {
  console.log('切换视图模式:', mode);

  // 如果从统计视图切换出去，清理图表实例
  if (viewMode.value === 'stats' && mode !== 'stats') {
    disposeAllCharts();
  }

  viewMode.value = mode;
  await nextTick();
  await loadData();
}

/** 获取时间槽样式类 */
function getSlotClass(slot) {
  return `slot-${getStatusType(slot.status)}`;
}

/** 获取星期文本 */
function getWeekDayText(weekDay) {
  const weekMap = {
    'Mon': '周一', 'Tue': '周二', 'Wed': '周三', 'Thu': '周四',
    'Fri': '周五', 'Sat': '周六', 'Sun': '周日'
  };
  return weekMap[weekDay] || weekDay;
}

/** 格式化日期标签 */
function formatDateLabel(date) {
  const d = new Date(date);
  return `${date} (${getWeekDayText(d.toLocaleDateString('en-US', { weekday: 'short' }))})`;
}

/** 获取时间槽提示信息 */
function getSlotTooltip(slot) {
  return `${slot.startTime}-${slot.endTime} ${getStatusText(slot.status)} ${slot.timeRange?.name || ''}`;
}

/** 根据时间和咨询师获取时间槽（当前日期） */
function getSlotByTimeAndCounselor(time, counselorId) {
  return currentDateSlots.value.filter(slot =>
    slot.startTime && slot.startTime.substring(0, 5) === time &&
    slot.counselorId === counselorId
  );
}

/** 网格日期变化处理 */
function handleGridDateChange(date) {
  // 确保日期格式正确
  if (date instanceof Date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    selectedGridDate.value = `${year}-${month}-${day}`;
  } else {
    selectedGridDate.value = date;
  }

  if (selectedGridDate.value) {
    loadGridData();
  }
}

/** 日期标签页点击 */
function handleDateTabClick(tab) {
  activeDate.value = tab.name;
}

/** 查看时间槽详情 */
function viewSlotDetail(slot) {
  currentSlot.value = slot;
  detailOpen.value = true;
}

/** 修改时间槽 */
function editSlot(slot) {
  // 这里可以实现编辑功能
  ElMessage.info('编辑功能待实现');
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加时间槽";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const timeSlotId = row.id || ids.value[0];
  getTimeSlot(timeSlotId).then(response => {
    console.log('获取时间槽详情数据:', response.data);
    Object.assign(form, response.data);

    // 处理时间段ID字段 - 兼容不同的字段名
    if (response.data.rangeId && !form.timeRangeId) {
      form.timeRangeId = response.data.rangeId;
    }
    if (response.data.timeRange && response.data.timeRange.id && !form.timeRangeId) {
      form.timeRangeId = response.data.timeRange.id;
    }

    // 确保状态字段是字符串类型，以便在下拉框中正确显示
    if (form.status !== undefined && form.status !== null) {
      form.status = String(form.status);
    }

    // 确保日期字段格式正确
    if (form.dateKey && typeof form.dateKey === 'string') {
      // 如果是完整的日期时间字符串，只取日期部分
      if (form.dateKey.includes(' ')) {
        form.dateKey = form.dateKey.split(' ')[0];
      }
    }

    console.log('处理后的表单数据:', form);
    open.value = true;
    title.value = "修改时间槽";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["timeSlotRef"].validate(valid => {
    if (valid) {
      if (form.id != undefined) {
        updateTimeSlot(form).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTimeSlot(form).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const timeSlotIds = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除所选时间槽数据?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return delTimeSlot(timeSlotIds);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => { });
}

/** 公开状态修改 */
function handlePublicChange(row) {
  let text = row.isPublic === 1 ? "公开" : "私有";
  ElMessageBox.confirm('确认要设置为"' + text + '"吗?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return updateTimeSlot(row);
  }).then(() => {
    ElMessage.success("设置成功");
  }).catch(() => {
    row.isPublic = row.isPublic === 1 ? 0 : 1;
  });
}

/** 显示生成对话框 */
function showGenerateDialog() {
  generateForm.counselorId = undefined;
  generateForm.startDate = undefined;
  generateForm.endDate = undefined;
  generateOpen.value = true;
}

/** 生成时间槽 */
function handleGenerate() {
  proxy.$refs["generateRef"].validate(valid => {
    if (valid) {
      const { counselorId, startDate, endDate } = generateForm;

      const apiCall = counselorId ?
        generateSlots(counselorId, startDate, endDate) :
        generateSlotsForAll(startDate, endDate);

      apiCall.then(response => {
        ElMessage.success(response.msg);
        generateOpen.value = false;
        getList();
      });
    }
  });
}

/** 将 Date 对象转换为 YYYY-MM-DD 格式字符串 */
function formatDateToString(date) {
  if (!date) return undefined;
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/** 显示清理对话框 */
function showCleanDialog() {
  cleanForm.beforeDate = undefined;
  cleanOpen.value = true;
}

/** 清理过期时间槽 */
function handleClean() {
  proxy.$refs["cleanRef"].validate(valid => {
    if (valid) {
      cleanExpiredSlots(cleanForm.beforeDate).then(response => {
        ElMessage.success(response.msg);
        cleanOpen.value = false;
        getList();
      });
    }
  });
}

/** 查看预约信息 */
function viewAppointment(row) {
  ElMessageBox.alert(row.appointmentInfo || '暂无预约信息', '预约详情', {
    confirmButtonText: '确定'
  });
}

/** 初始化图表 */
async function initCharts() {
  // 先设置所有图表为加载状态
  dateChartLoading.value = true;
  counselorChartLoading.value = true;
  statusChartLoading.value = true;
  timeRangeChartLoading.value = true;
  heatmapChartLoading.value = true;

  // 等待DOM更新完成
  await nextTick();

  // 确保容器可见并且有尺寸
  if (viewMode.value !== 'stats') {
    return;
  }

  // 等待一小段时间确保DOM完全渲染
  await new Promise(resolve => setTimeout(resolve, 100));

  // 检查并初始化每个图表
  const charts = [
    { ref: dateChart, init: initDateChart, name: '日期统计' },
    { ref: counselorChart, init: initCounselorChart, name: '咨询师统计' },
    { ref: statusChart, init: initStatusChart, name: '状态分布' },
    { ref: timeRangeChart, init: initTimeRangeChart, name: '时间段分布' },
    { ref: heatmapChart, init: initHeatmapChart, name: '热力图' }
  ];

  for (const chart of charts) {
    if (chart.ref.value && chart.ref.value.clientWidth && chart.ref.value.clientHeight) {
      try {
        await chart.init();
      } catch (error) {
        console.error(`初始化${chart.name}图表失败:`, error);
      }
    } else {
      console.warn(`${chart.name}图表容器尺寸未就绪`);
    }
  }
}

/** 刷新图表（当咨询师数据更新时） */
async function refreshCharts() {
  if (viewMode.value === 'stats') {
    // 确保在下一个 tick 执行，以等待数据更新
    await nextTick();
    // 给DOM一些时间来调整尺寸
    await new Promise(resolve => setTimeout(resolve, 100));
    await initCharts();
  }
}

/** 初始化日期统计图表 */
async function initDateChart() {
  if (!dateChart.value) return;
  dateChartLoading.value = true;

  try {
    // 销毁现有实例
    if (chartInstances.dateChart) {
      chartInstances.dateChart.dispose();
    }

    // 检查数据是否有效
    if (!gridData.value || !Array.isArray(gridData.value)) {
      console.warn('日期图表数据无效');
      return;
    }

    // 创建新实例
    chartInstances.dateChart = echarts.init(dateChart.value);

    // 按日期统计
    const dateStats = {};
    gridData.value.forEach(slot => {
      if (!slot || !slot.dateKey) return;
      const date = slot.dateKey;
      if (!dateStats[date]) {
        dateStats[date] = { available: 0, booked: 0, unavailable: 0 };
      }
      const status = Number(slot.status);
      switch (status) {
        case 0: dateStats[date].available++; break;
        case 1: dateStats[date].booked++; break;
        case 2: dateStats[date].unavailable++; break;
      }
    });

    const dates = Object.keys(dateStats).sort();
    if (dates.length === 0) {
      console.warn('没有有效的日期数据');
      return;
    }

    const availableData = dates.map(date => dateStats[date].available);
    const bookedData = dates.map(date => dateStats[date].booked);
    const unavailableData = dates.map(date => dateStats[date].unavailable);

    const option = {
      tooltip: { trigger: 'axis' },
      legend: { data: ['可用', '已预约', '不可用'] },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '可用',
          type: 'bar',
          data: availableData,
          itemStyle: { color: '#67c23a' }
        },
        {
          name: '已预约',
          type: 'bar',
          data: bookedData,
          itemStyle: { color: '#e6a23c' }
        },
        {
          name: '不可用',
          type: 'bar',
          data: unavailableData,
          itemStyle: { color: '#f56c6c' }
        }
      ]
    };

    chartInstances.dateChart.clear();
    chartInstances.dateChart.setOption(option, true);
  } catch (error) {
    handleChartError('日期统计', error);
  } finally {
    dateChartLoading.value = false;
  }
}

/** 初始化咨询师统计图表 */
async function initCounselorChart() {
  if (!counselorChart.value) return;
  counselorChartLoading.value = true;

  try {
    if (chartInstances.counselorChart) {
      chartInstances.counselorChart.dispose();
    }

    // 检查数据是否有效
    if (!detailStats.value || !Array.isArray(detailStats.value)) {
      console.warn('咨询师统计数据无效');
      return;
    }

    chartInstances.counselorChart = echarts.init(counselorChart.value);

    const data = detailStats.value
      .filter(item => item && item.counselorId && typeof item.total === 'number')
      .map(item => ({
        name: getCounselorName(item.counselorId),
        value: item.total
      }));

    if (data.length === 0) {
      console.warn('没有有效的咨询师统计数据');
      return;
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: '时间槽数量',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    chartInstances.counselorChart.setOption(option);
  } catch (error) {
    console.error('初始化咨询师图表失败:', error);
  } finally {
    counselorChartLoading.value = false;
  }
}

/** 初始化状态统计图表 */
async function initStatusChart() {
  if (!statusChart.value) return;
  statusChartLoading.value = true;

  try {
    // 销毁现有实例
    if (chartInstances.statusChart) {
      chartInstances.statusChart.dispose();
      chartInstances.statusChart = null;
    }

    // 检查数据是否有效
    if (!statistics.value) {
      console.warn('状态统计数据无效');
      statusChartLoading.value = false;
      return;
    }

    // 等待DOM更新
    await nextTick();

    // 创建新实例
    chartInstances.statusChart = echarts.init(statusChart.value);

    const { available = 0, booked = 0, unavailable = 0 } = statistics.value;
    const data = [
      { name: '可用', value: available, itemStyle: { color: '#67c23a' } },
      { name: '已预约', value: booked, itemStyle: { color: '#e6a23c' } },
      { name: '不可用', value: unavailable, itemStyle: { color: '#f56c6c' } }
    ].filter(item => item.value > 0);

    if (data.length === 0) {
      console.warn('没有有效的状态统计数据');
      statusChartLoading.value = false;
      return;
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: data.map(item => item.name)
      },
      series: [{
        name: '状态分布',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    // 清除并设置配置项
    chartInstances.statusChart.clear();
    chartInstances.statusChart.setOption(option, true);

    // 绑定点击事件
    chartInstances.statusChart.on('click', function (params) {
      if (!params || !params.data) return;
      console.log('点击了状态:', params.name, '数量:', params.value);
    });
  } catch (error) {
    handleChartError('状态分布', error);
  } finally {
    statusChartLoading.value = false;
  }
}

/** 初始化时间段统计图表 */
async function initTimeRangeChart() {
  if (!timeRangeChart.value) return;
  timeRangeChartLoading.value = true;

  try {
    if (chartInstances.timeRangeChart) {
      chartInstances.timeRangeChart.dispose();
    }

    // 检查数据是否有效
    if (!gridData.value || !Array.isArray(gridData.value)) {
      console.warn('时间段统计数据无效');
      return;
    }

    chartInstances.timeRangeChart = echarts.init(timeRangeChart.value);

    const timeRangeStats = {};
    gridData.value.forEach(slot => {
      if (!slot || !slot.timeRange) return;
      const timeRange = slot.timeRange.name || '未知';
      timeRangeStats[timeRange] = (timeRangeStats[timeRange] || 0) + 1;
    });

    const data = Object.entries(timeRangeStats)
      .filter(([name, value]) => name && value > 0)
      .map(([name, value]) => ({ name, value }));

    if (data.length === 0) {
      console.warn('没有有效的时间段统计数据');
      return;
    }

    const option = {
      tooltip: { trigger: 'item' },
      series: [{
        name: '时间段分布',
        type: 'pie',
        radius: '50%',
        data: data
      }]
    };

    chartInstances.timeRangeChart.setOption(option);
  } catch (error) {
    console.error('初始化时间段图表失败:', error);
  } finally {
    timeRangeChartLoading.value = false;
  }
}

/** 初始化热力图 */
async function initHeatmapChart() {
  if (!heatmapChart.value) return;
  heatmapChartLoading.value = true;

  try {
    if (chartInstances.heatmapChart) {
      chartInstances.heatmapChart.dispose();
    }

    // 检查数据是否有效
    if (!gridData.value || !Array.isArray(gridData.value)) {
      console.warn('热力图数据无效');
      return;
    }

    chartInstances.heatmapChart = echarts.init(heatmapChart.value);

    // 过滤有效数据
    const validSlots = gridData.value.filter(slot =>
      slot &&
      slot.counselorId &&
      slot.startTime &&
      typeof slot.startTime === 'string'
    );

    if (validSlots.length === 0) {
      console.warn('没有有效的热力图数据');
      return;
    }

    const counselorIds = [...new Set(validSlots.map(slot => slot.counselorId))];
    const counselors = counselorIds.map(id => getCounselorName(id));
    const hours = Array.from(new Set(validSlots.map(slot => slot.startTime.substring(0, 5)))).sort();

    if (counselors.length === 0 || hours.length === 0) {
      console.warn('热力图维度数据无效');
      return;
    }

    const heatmapData = [];
    counselorIds.forEach((counselorId, counselorIndex) => {
      hours.forEach((hour, hourIndex) => {
        const count = validSlots.filter(slot =>
          slot.counselorId === counselorId &&
          slot.startTime.substring(0, 5) === hour
        ).length;
        heatmapData.push([hourIndex, counselorIndex, count]);
      });
    });

    const option = {
      tooltip: {
        position: 'top',
        formatter: function (params) {
          if (!params || !params.value) return '';
          return `${counselors[params.value[1]]}<br/>${hours[params.value[0]]}: ${params.value[2]}个时间槽`;
        }
      },
      grid: {
        height: '50%',
        top: '10%'
      },
      xAxis: {
        type: 'category',
        data: hours,
        splitArea: { show: true }
      },
      yAxis: {
        type: 'category',
        data: counselors,
        splitArea: { show: true }
      },
      visualMap: {
        min: 0,
        max: Math.max(...heatmapData.map(item => item[2]), 1),
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%'
      },
      series: [{
        name: '时间槽数量',
        type: 'heatmap',
        data: heatmapData,
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    };

    chartInstances.heatmapChart.setOption(option);
  } catch (error) {
    console.error('初始化热力图失败:', error);
  } finally {
    heatmapChartLoading.value = false;
  }
}

// 监听咨询师列表变化，更新统计数据
watch(counselorList, (newList) => {
  if (newList.length > 0 && viewMode.value === 'stats' && gridData.value.length > 0) {
    // 延迟一点时间确保计算属性已更新
    setTimeout(() => {
      refreshCharts();
    }, 200);
  }
}, { deep: true });

/** 获取门店列表 */
function getStoreList() {
  listStore().then(response => {
    storeList.value = response.rows || response.data || [];
  });
}

/** 根据门店ID获取门店名称 */
function getStoreName(centerId) {
  const store = storeList.value.find(item => item.id === centerId);
  return store ? store.name : `门店${centerId}`;
}

onMounted(() => {
  getCounselorList();
  getTimeRangeList();
  getStoreList();
  loadData();
});
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.grid-container {
  margin-top: 20px;
}

.date-tabs {
  margin-bottom: 20px;
}

.time-grid {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.grid-header {
  display: flex;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.time-label {
  width: 80px;
  padding: 10px;
  text-align: center;
  font-weight: bold;
  border-right: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.counselor-header {
  flex: 1;
  padding: 10px;
  text-align: center;
  font-weight: bold;
  border-right: 1px solid #e4e7ed;
}

.time-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
}

.time-row:last-child {
  border-bottom: none;
}

.slot-cell {
  flex: 1;
  min-height: 60px;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.slot-item {
  width: 100%;
  height: 100%;
  padding: 5px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 12px;
}

.slot-item:hover {
  transform: scale(1.05);
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.slot-success {
  background-color: #f0f9ff;
  color: #67c23a;
  border: 1px solid #67c23a;
}

.slot-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #e6a23c;
}

.slot-danger {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

.slot-info {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #909399;
}

.empty-slot {
  color: #c0c4cc;
  font-size: 14px;
}

.slot-time {
  font-weight: bold;
  margin-bottom: 2px;
}

.slot-status {
  font-size: 10px;
}

.stats-container {
  margin-top: 20px;
}

.loading-container {
  text-align: center;
  padding: 50px;
  color: #666;
}

.loading-container .el-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.date-selector {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.no-data,
.select-date-tip {
  padding: 50px;
  text-align: center;
}

.chart-container {
  position: relative;
  min-height: 300px;
}

/* 提高加载样式的优先级 */
.chart-container .chart-loading {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(255, 255, 255, 0.9) !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: center !important;
  align-items: center !important;
  z-index: 100 !important;
}

.chart-container .chart-loading .el-icon {
  font-size: 32px !important;
  color: var(--el-color-primary) !important;
  margin-bottom: 16px !important;
}

.chart-container .chart-loading p {
  color: var(--el-text-color-secondary) !important;
  font-size: 14px !important;
  margin: 0 !important;
}

/* 确保图表容器有最小尺寸 */
.chart-container {
  position: relative !important;
  min-height: 300px !important;
  min-width: 200px !important;
  width: 100% !important;
  height: 100% !important;
}

/* 图表容器内的实际图表 */
.chart-container>div:not(.chart-loading) {
  width: 100% !important;
  height: 100% !important;
  min-height: inherit !important;
}
</style>
