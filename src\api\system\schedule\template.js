import request from '@/utils/request'

// 查询排班模板列表
export function listTemplate(query) {
  return request({
    url: '/system/schedule/template/list',
    method: 'get',
    params: query
  })
}

// 导出排班模板列表
export function exportTemplate(query) {
  return request({
    url: '/system/schedule/template/export',
    method: 'post',
    params: query
  })
}

// 获取排班模板详细信息
export function getTemplate(id) {
  return request({
    url: '/system/schedule/template/' + id,
    method: 'get'
  })
}

// 根据咨询师ID查询排班模板
export function getTemplatesByCounselorId(counselorId) {
  return request({
    url: `/system/schedule/template/counselor/${counselorId}`,
    method: 'get'
  })
}

// 查询咨询师的默认模板
export function getDefaultTemplate(counselorId) {
  return request({
    url: `/system/schedule/template/counselor/${counselorId}/default`,
    method: 'get'
  })
}

// 查询在指定日期有效的模板
export function getEffectiveTemplate(counselorId, date) {
  return request({
    url: `/system/schedule/template/counselor/${counselorId}/effective`,
    method: 'get',
    params: { date }
  })
}

// 新增排班模板
export function addTemplate(data) {
  return request({
    url: '/system/schedule/template',
    method: 'post',
    data: data
  })
}

// 修改排班模板
export function updateTemplate(data) {
  return request({
    url: '/system/schedule/template',
    method: 'put',
    data: data
  })
}

// 设置默认模板
export function setDefaultTemplate(counselorId, templateId) {
  return request({
    url: `/system/schedule/template/setDefault/${counselorId}/${templateId}`,
    method: 'put'
  })
}

// 删除排班模板
export function delTemplate(ids) {
  return request({
    url: '/system/schedule/template/' + ids,
    method: 'delete'
  })
}

// 校验模板名称是否唯一
export function checkTemplateNameUnique(data) {
  return request({
    url: '/system/schedule/template/checkTemplateNameUnique',
    method: 'post',
    data: data
  })
}

// 为咨询师创建默认排班模板
export function createDefaultTemplate(counselorId, centerId = 1) {
  return request({
    url: `/system/schedule/template/createDefault/${counselorId}`,
    method: 'post',
    params: { centerId }
  })
}

// 复制排班模板
export function copyTemplate(templateId, newName) {
  return request({
    url: `/system/schedule/template/copy/${templateId}`,
    method: 'post',
    params: { newName }
  })
}
