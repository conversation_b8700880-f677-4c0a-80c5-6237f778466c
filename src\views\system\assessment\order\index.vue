<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户昵称" prop="userNickname">
        <el-input
          v-model="queryParams.userNickname"
          placeholder="请输入用户昵称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="量表名称" prop="scaleName">
        <el-input
          v-model="queryParams.scaleName"
          placeholder="请输入量表名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择订单状态" clearable>
          <el-option
            v-for="dict in sys_assessment_order_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="支付状态" prop="payStatus">
        <el-select v-model="queryParams.payStatus" placeholder="请选择支付状态" clearable>
          <el-option
            v-for="dict in sys_pay_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" style="width: 308px">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:assessment:order:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:assessment:order:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:assessment:order:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:assessment:order:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="orderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单号" align="center" prop="orderNo" width="180" />
      <el-table-column label="用户昵称" align="center" prop="userNickname" />
      <el-table-column label="量表名称" align="center" prop="scaleName" />
      <el-table-column label="订单金额" align="center" prop="amount">
        <template #default="scope">
          <span>¥{{ scope.row.amount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="payStatus">
        <template #default="scope">
          <dict-tag :options="sys_pay_status" :value="scope.row.payStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="订单状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="sys_assessment_order_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['system:assessment:order:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:assessment:order:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:assessment:order:remove']">删除</el-button>
          <el-button 
            v-if="scope.row.payStatus === '1' && scope.row.status !== '4'"
            link 
            type="warning" 
            icon="RefreshLeft" 
            @click="handleRefund(scope.row)" 
            v-hasPermi="['system:assessment:order:refund']"
          >退款</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改订单对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="orderRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户" prop="userId">
          <el-select v-model="form.userId" placeholder="请选择用户" filterable>
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.nickName"
              :value="user.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="量表" prop="scaleId">
          <el-select v-model="form.scaleId" placeholder="请选择量表" filterable>
            <el-option
              v-for="scale in scaleList"
              :key="scale.id"
              :label="scale.name"
              :value="scale.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="订单金额" prop="amount">
          <el-input-number v-model="form.amount" :precision="2" :min="0" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ detailForm.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称">{{ detailForm.userNickname }}</el-descriptions-item>
        <el-descriptions-item label="量表名称">{{ detailForm.scaleName }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">¥{{ detailForm.amount }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">
          <dict-tag :options="sys_pay_status" :value="detailForm.payStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          <dict-tag :options="sys_assessment_order_status" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ parseTime(detailForm.payTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detailForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 退款对话框 -->
    <el-dialog title="订单退款" v-model="refundOpen" width="500px" append-to-body>
      <el-form ref="refundRef" :model="refundForm" :rules="refundRules" label-width="80px">
        <el-form-item label="退款金额" prop="refundAmount">
          <el-input-number v-model="refundForm.refundAmount" :precision="2" :min="0" :max="refundForm.maxAmount" />
          <div class="el-form-item__error">最大可退款金额：¥{{ refundForm.maxAmount }}</div>
        </el-form-item>
        <el-form-item label="退款原因" prop="refundReason">
          <el-input v-model="refundForm.refundReason" type="textarea" placeholder="请输入退款原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRefund">确 定</el-button>
          <el-button @click="refundOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AssessmentOrder">
import { 
  listAssessmentOrder, 
  getAssessmentOrder, 
  delAssessmentOrder, 
  addAssessmentOrder, 
  updateAssessmentOrder,
  exportAssessmentOrder,
  refundAssessmentOrder
} from "@/api/system/assessment/order";
import { listScale } from "@/api/system/assessment/scale";
import { listUser } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { sys_assessment_order_status, sys_pay_status } = proxy.useDict('sys_assessment_order_status', 'sys_pay_status');

const orderList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const refundOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const userList = ref([]);
const scaleList = ref([]);

const data = reactive({
  form: {},
  detailForm: {},
  refundForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: null,
    userNickname: null,
    scaleName: null,
    status: null,
    payStatus: null,
  },
  rules: {
    userId: [
      { required: true, message: "用户不能为空", trigger: "change" }
    ],
    scaleId: [
      { required: true, message: "量表不能为空", trigger: "change" }
    ],
    amount: [
      { required: true, message: "订单金额不能为空", trigger: "blur" }
    ]
  },
  refundRules: {
    refundAmount: [
      { required: true, message: "退款金额不能为空", trigger: "blur" }
    ],
    refundReason: [
      { required: true, message: "退款原因不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, refundForm, rules, refundRules } = toRefs(data);

/** 查询订单列表 */
function getList() {
  loading.value = true;
  listAssessmentOrder(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    orderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    userId: null,
    scaleId: null,
    amount: null,
    remark: null
  };
  proxy.resetForm("orderRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  getUserList();
  getScaleList();
  open.value = true;
  title.value = "添加订单";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  getUserList();
  getScaleList();
  const id = row.id || ids.value;
  getAssessmentOrder(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改订单";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["orderRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateAssessmentOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAssessmentOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const orderIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除订单编号为"' + orderIds + '"的数据项？').then(function() {
    return delAssessmentOrder(orderIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/assessment/order/export', {
    ...queryParams.value
  }, `order_${new Date().getTime()}.xlsx`)
}

/** 详情按钮操作 */
function handleDetail(row) {
  getAssessmentOrder(row.id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 退款按钮操作 */
function handleRefund(row) {
  refundForm.value = {
    id: row.id,
    maxAmount: row.amount,
    refundAmount: row.amount,
    refundReason: ''
  };
  refundOpen.value = true;
}

/** 提交退款 */
function submitRefund() {
  proxy.$refs["refundRef"].validate(valid => {
    if (valid) {
      refundAssessmentOrder(refundForm.value.id, refundForm.value).then(response => {
        proxy.$modal.msgSuccess("退款申请提交成功");
        refundOpen.value = false;
        getList();
      });
    }
  });
}

/** 获取用户列表 */
function getUserList() {
  listUser().then(response => {
    userList.value = response.rows;
  });
}

/** 获取量表列表 */
function getScaleList() {
  listScale().then(response => {
    scaleList.value = response.rows;
  });
}

onMounted(() => {
  getList();
});
</script>
