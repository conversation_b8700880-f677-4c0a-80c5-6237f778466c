# 音频文件动态分析功能使用指南

## 功能概述

音频文件动态分析功能可以在用户上传音频文件后，自动获取音频的详细信息，包括：

- **音频时长**：精确到秒
- **文件格式**：mp3、wav、flac等
- **文件大小**：自动格式化显示（B、KB、MB、GB）
- **比特率**：音频质量指标（kbps）
- **采样率**：音频采样频率（Hz）

## 工作原理

### 1. 双重分析机制

系统采用前端+后端双重分析机制，确保获取准确的音频信息：

1. **前端分析**（快速获取基本信息）
   - 使用HTML5 Audio API获取音频时长
   - 从文件名提取格式信息
   - 从文件对象获取文件大小
   - 设置格式对应的默认比特率和采样率

2. **后端分析**（获取详细信息）
   - 调用后端API `/system/meditation/audio/analyze`
   - 使用专业音频分析库获取精确的音频参数
   - 覆盖前端分析的默认值

### 2. 分析流程

```
用户上传音频文件
    ↓
前端获取文件基本信息（大小、格式）
    ↓
前端Audio API分析（时长）
    ↓
设置默认音频参数（比特率、采样率）
    ↓
调用后端API进一步分析
    ↓
更新精确的音频参数
    ↓
完成分析，用户可编辑
```

## 使用方法

### 1. 上传音频文件

1. 在音频管理页面点击"新增音频"
2. 在弹出的对话框中点击"音频文件"上传区域
3. 选择音频文件（支持mp3、wav、flac格式）
4. 系统会显示"正在分析音频文件..."提示

### 2. 查看分析结果

分析完成后，以下字段会自动填充：

- **时长(秒)**：显示音频总时长，下方会显示格式化的时长（如"3:45"）
- **文件格式**：自动识别的音频格式
- **文件大小**：格式化显示的文件大小
- **比特率(kbps)**：音频比特率
- **采样率(Hz)**：音频采样率

### 3. 手动调整

如果自动分析的结果不准确，用户可以手动调整：

- 时长、比特率、采样率字段在分析完成后可以手动编辑
- 文件格式和文件大小字段为只读，不可编辑

## 默认值设置

当无法获取精确音频参数时，系统会根据文件格式设置默认值：

### MP3格式
- 比特率：128 kbps
- 采样率：44100 Hz

### WAV格式
- 比特率：1411 kbps（CD质量）
- 采样率：44100 Hz

### FLAC格式
- 比特率：1000 kbps（无损压缩）
- 采样率：44100 Hz

## 错误处理

### 1. 前端分析失败
- 显示警告信息但不阻断流程
- 设置默认音频参数
- 继续尝试后端分析

### 2. 后端分析失败
- 使用前端分析结果
- 在控制台显示警告信息
- 确保有默认值可用

### 3. 分析超时
- 前端分析设置5秒超时
- 超时后设置默认值
- 继续后端分析流程

## 技术实现

### 前端实现

```javascript
// 处理音频文件变化
function handleAudioFileChange(fileUrl, fileInfo) {
  audioAnalyzing.value = true;
  
  // 设置基本信息
  form.value.fileSize = fileInfo.size;
  fileSizeDisplay.value = formatFileSize(fileInfo.size);
  form.value.fileFormat = getFileExtension(fileInfo.name);
  
  // 前端分析 + 后端分析
  analyzeAudioFileLocal(fileUrl, fileExtension)
    .then(() => analyzeAudioFile(fileUrl))
    .then(updateAudioInfo)
    .finally(() => audioAnalyzing.value = false);
}
```

### 后端API接口

```javascript
// 分析音频文件
export function analyzeAudioFile(audioUrl) {
  return request({
    url: '/system/meditation/audio/analyze',
    method: 'post',
    data: { audioUrl }
  })
}
```

## 用户界面

### 1. 分析状态指示

```html
<div v-if="audioAnalyzing" class="audio-analyzing">
  <el-icon class="is-loading"><Loading /></el-icon>
  <span class="ml-2">正在分析音频文件...</span>
</div>
```

### 2. 字段状态

- 分析期间相关字段显示为禁用状态
- 显示"上传音频后自动获取"占位符
- 时长字段下方显示格式化的时长信息

### 3. 样式设计

- 分析提示使用蓝色主题色
- 加载图标旋转动画
- 提示信息字体大小14px

## 注意事项

### 1. 浏览器兼容性
- 需要支持HTML5 Audio API
- 某些浏览器可能限制跨域音频访问
- 建议在现代浏览器中使用

### 2. 文件大小限制
- 大文件可能导致分析时间较长
- 建议设置合理的文件大小限制
- 考虑添加上传进度显示

### 3. 网络环境
- 后端分析依赖网络连接
- 网络不稳定时可能分析失败
- 前端分析可作为备选方案

### 4. 音频格式支持
- 主要支持常见的音频格式
- 特殊格式可能无法正确分析
- 建议用户使用标准格式

## 扩展功能

### 1. 可能的增强
- 音频波形图显示
- 音频质量评估
- 音频转码功能
- 批量音频分析

### 2. 性能优化
- 音频文件缓存
- 分析结果缓存
- 异步分析队列
- 进度条显示

## 故障排除

### 1. 分析失败
- 检查音频文件是否损坏
- 确认文件格式是否支持
- 查看浏览器控制台错误信息

### 2. 时长不准确
- 某些音频文件可能包含元数据错误
- 可以手动调整时长字段
- 建议使用标准音频编码

### 3. 比特率显示异常
- VBR（可变比特率）文件可能显示平均值
- FLAC等无损格式比特率为估算值
- 可根据实际情况手动调整

这个功能大大提升了音频管理的用户体验，减少了手动输入的工作量，提高了数据的准确性。
