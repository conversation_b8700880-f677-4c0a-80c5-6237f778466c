# 题目管理接口更新说明

## 更新概述

根据后端接口路径的变更，已完成题目管理相关API接口的前端适配更新。

## 主要变更

### 1. 接口路径更新
- **原路径**：`/system/assessment/question`
- **新路径**：`/system/question`

### 2. 权限标识更新
- **原权限**：`system:assessment:question:*`
- **新权限**：`system:question:*`

## 详细更新内容

### 1. API文件更新 (`src/api/system/assessment/question.js`)

#### 基础CRUD接口
```javascript
// 查询题目列表
export function listQuestion(query) {
  return request({
    url: '/system/question/list',  // 更新路径
    method: 'get',
    params: query
  })
}

// 查询题目详细
export function getQuestion(id) {
  return request({
    url: '/system/question/' + id,  // 更新路径
    method: 'get'
  })
}

// 新增题目
export function addQuestion(data) {
  return request({
    url: '/system/question',  // 更新路径
    method: 'post',
    data: data
  })
}

// 修改题目
export function updateQuestion(data) {
  return request({
    url: '/system/question',  // 更新路径
    method: 'put',
    data: data
  })
}

// 删除题目
export function delQuestion(ids) {
  return request({
    url: '/system/question/' + ids,  // 更新路径
    method: 'delete'
  })
}
```

#### 新增功能接口
根据后端Controller新增的功能，添加了以下API接口：

1. **获取题目详情（包含选项信息）**
   ```javascript
   export function getQuestionDetails(id)
   ```

2. **根据量表ID查询题目列表（包含选项）**
   ```javascript
   export function getQuestionsWithOptionsByScale(scaleId)
   ```

3. **根据题目序号查询题目**
   ```javascript
   export function getQuestionByNo(scaleId, questionNo)
   ```

4. **查询题目统计信息**
   ```javascript
   export function getQuestionStats(scaleId)
   ```

5. **复制题目**
   ```javascript
   export function copyQuestions(sourceScaleId, targetScaleId)
   ```

6. **验证题目配置**
   ```javascript
   export function validateQuestions(scaleId)
   ```

7. **自动生成题目序号**
   ```javascript
   export function generateQuestionNumbers(scaleId)
   ```

8. **重新排序题目**
   ```javascript
   export function reorderQuestions(scaleId)
   ```

9. **更新题目显示顺序**
   ```javascript
   export function updateQuestionOrder(id, orderNum)
   ```

10. **批量更新题目状态**
    ```javascript
    export function batchUpdateQuestionStatus(ids, status)
    ```

11. **根据分量表查询题目**
    ```javascript
    export function getQuestionsBySubscale(subscaleId)
    ```

12. **查询必答题目**
    ```javascript
    export function getRequiredQuestions(scaleId)
    ```

13. **查询选答题目**
    ```javascript
    export function getOptionalQuestions(scaleId)
    ```

14. **随机获取题目**
    ```javascript
    export function getRandomQuestions(scaleId, count)
    ```

15. **导入题目**
    ```javascript
    export function importQuestions(scaleId, file)
    ```

16. **导出指定量表的题目**
    ```javascript
    export function exportQuestionsByScale(scaleId)
    ```

17. **获取导入模板**
    ```javascript
    export function getImportTemplate()
    ```

### 2. 页面文件更新

#### 题目管理页面 (`src/views/system/assessment/question/index.vue`)
- 更新所有权限标识：`system:assessment:question:*` → `system:question:*`

#### 量表管理页面 (`src/views/system/assessment/scale/index.vue`)
- 更新题目相关权限标识：`system:assessment:question:*` → `system:question:*`

### 3. 权限标识对照表

| 功能 | 原权限标识 | 新权限标识 |
|------|------------|------------|
| 查询题目 | `system:assessment:question:list` | `system:question:list` |
| 题目详情 | `system:assessment:question:query` | `system:question:query` |
| 新增题目 | `system:assessment:question:add` | `system:question:add` |
| 修改题目 | `system:assessment:question:edit` | `system:question:edit` |
| 删除题目 | `system:assessment:question:remove` | `system:question:remove` |
| 导出题目 | `system:assessment:question:export` | `system:question:export` |
| 导入题目 | `system:assessment:question:import` | `system:question:import` |
| 题目统计 | `system:assessment:question:stats` | `system:question:stats` |
| 复制题目 | `system:assessment:question:copy` | `system:question:copy` |
| 验证题目 | `system:assessment:question:validate` | `system:question:validate` |

## 后端接口对应关系

| 前端API函数 | 后端Controller方法 | 接口路径 |
|-------------|-------------------|----------|
| `listQuestion` | `list` | `GET /system/question/list` |
| `getQuestion` | `getInfo` | `GET /system/question/{id}` |
| `getQuestionDetails` | `getDetails` | `GET /system/question/details/{id}` |
| `addQuestion` | `add` | `POST /system/question` |
| `updateQuestion` | `edit` | `PUT /system/question` |
| `delQuestion` | `remove` | `DELETE /system/question/{ids}` |
| `getQuestionsByScale` | `questionsByScale` | `GET /system/question/scale/{scaleId}` |
| `getQuestionsWithOptionsByScale` | `questionsWithOptionsByScale` | `GET /system/question/scale/{scaleId}/with-options` |
| `getQuestionByNo` | `questionByNo` | `GET /system/question/scale/{scaleId}/no/{questionNo}` |
| `getQuestionStats` | `questionStats` | `GET /system/question/stats/{scaleId}` |
| `copyQuestions` | `copyQuestions` | `POST /system/question/copy` |
| `validateQuestions` | `validateQuestions` | `GET /system/question/validate/{scaleId}` |
| `generateQuestionNumbers` | `generateQuestionNumbers` | `POST /system/question/generate-numbers/{scaleId}` |
| `reorderQuestions` | `reorderQuestions` | `POST /system/question/reorder/{scaleId}` |
| `updateQuestionOrder` | `updateOrder` | `PUT /system/question/order` |
| `batchUpdateQuestionStatus` | `updateStatus` | `PUT /system/question/status` |
| `getQuestionsBySubscale` | `questionsBySubscale` | `GET /system/question/subscale/{subscaleId}` |
| `getRequiredQuestions` | `requiredQuestions` | `GET /system/question/required/{scaleId}` |
| `getOptionalQuestions` | `optionalQuestions` | `GET /system/question/optional/{scaleId}` |
| `getRandomQuestions` | `randomQuestions` | `GET /system/question/random/{scaleId}` |
| `importQuestions` | `importQuestions` | `POST /system/question/import/{scaleId}` |
| `exportQuestion` | `export` | `POST /system/question/export` |
| `exportQuestionsByScale` | `exportQuestions` | `POST /system/question/export/{scaleId}` |
| `getImportTemplate` | `importTemplate` | `POST /system/question/importTemplate` |

## 注意事项

1. **权限配置**：需要在后台管理系统中更新相应的权限配置
2. **数据字典**：确保题目类型等数据字典配置正确
3. **文件上传**：导入功能需要确保文件上传配置正确
4. **Excel模板**：需要准备题目导入的Excel模板

## 测试建议

1. 测试基础CRUD操作
2. 测试题目与量表的关联功能
3. 测试导入导出功能
4. 测试权限控制是否正常
5. 测试题目排序和状态管理功能

---

**更新时间**：2025-01-21  
**更新状态**：✅ 已完成
