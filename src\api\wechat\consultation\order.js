import request from '@/utils/request'

// 查询咨询订单列表
export function listConsultantOrder(query) {
  return request({
    url: '/system/consultantOrder/list',
    method: 'get',
    params: query
  })
}

// 导出咨询订单列表
export function exportConsultantOrder(query) {
  return request({
    url: '/system/consultantOrder/export',
    method: 'post',
    params: query
  })
}

// 获取咨询订单详细信息
export function getConsultantOrder(id) {
  return request({
    url: `/system/consultantOrder/${id}`,
    method: 'get'
  })
}

// 根据订单号查询订单
export function getConsultantOrderByOrderNo(orderNo) {
  return request({
    url: `/system/consultantOrder/orderNo/${orderNo}`,
    method: 'get'
  })
}

// 获取咨询订单详细信息（包含关联信息）
export function getConsultantOrderDetails(id) {
  return request({
    url: `/system/consultantOrder/details/${id}`,
    method: 'get'
  })
}

// 新增咨询订单
export function addConsultantOrder(data) {
  return request({
    url: '/system/consultantOrder',
    method: 'post',
    data: data
  })
}

// 修改咨询订单
export function updateConsultantOrder(data) {
  return request({
    url: '/system/consultantOrder',
    method: 'put',
    data: data
  })
}

// 删除咨询订单
export function delConsultantOrder(ids) {
  return request({
    url: `/system/consultantOrder/${ids}`,
    method: 'delete'
  })
}

// 根据用户ID查询订单列表
export function getConsultantOrdersByUser(userId) {
  return request({
    url: `/system/consultantOrder/user/${userId}`,
    method: 'get'
  })
}

// 根据咨询师ID查询订单列表
export function getConsultantOrdersByConsultant(consultantId) {
  return request({
    url: `/system/consultantOrder/consultant/${consultantId}`,
    method: 'get'
  })
}

// 更新订单支付状态
export function updateConsultantOrderPaymentStatus(orderNo, status, paymentMethod, paymentTime) {
  return request({
    url: `/system/consultantOrder/payment/${orderNo}`,
    method: 'post',
    params: {
      status: status,
      paymentMethod: paymentMethod,
      paymentTime: paymentTime
    }
  })
}

// 更新订单状态
export function updateConsultantOrderStatus(id, status) {
  return request({
    url: `/system/consultantOrder/status/${id}`,
    method: 'post',
    params: {
      status: status
    }
  })
}

// 取消订单
export function cancelConsultantOrder(id, cancelReason) {
  return request({
    url: `/system/consultantOrder/cancel/${id}`,
    method: 'post',
    params: {
      cancelReason: cancelReason
    }
  })
}

// 退款订单
export function refundConsultantOrder(id, refundAmount, refundReason) {
  return request({
    url: `/system/consultantOrder/refund/${id}`,
    method: 'post',
    params: {
      refundAmount: refundAmount,
      refundReason: refundReason
    }
  })
}

// 检查时间段冲突
export function checkConsultantOrderTimeConflict(consultantId, startTime, endTime, excludeOrderId) {
  return request({
    url: '/system/consultantOrder/checkConflict',
    method: 'get',
    params: {
      consultantId: consultantId,
      startTime: startTime,
      endTime: endTime,
      excludeOrderId: excludeOrderId
    }
  })
}

// 查询即将到期的订单
export function getExpiringConsultantOrders(minutes = 30) {
  return request({
    url: '/system/consultantOrder/expiring',
    method: 'get',
    params: {
      minutes: minutes
    }
  })
}

// 查询已过期的订单
export function getExpiredConsultantOrders() {
  return request({
    url: '/system/consultantOrder/expired',
    method: 'get'
  })
}

// 处理过期订单
export function processExpiredConsultantOrders() {
  return request({
    url: '/system/consultantOrder/processExpired',
    method: 'post'
  })
}

// 获取用户订单统计
export function getUserConsultantOrderStats(userId) {
  return request({
    url: `/system/consultantOrder/statistics/user/${userId}`,
    method: 'get'
  })
}

// 获取咨询师订单统计
export function getConsultantOrderStats(consultantId) {
  return request({
    url: `/system/consultantOrder/statistics/consultant/${consultantId}`,
    method: 'get'
  })
}

// 统计订单收入
export function getConsultantOrderIncome(consultantId, startTime, endTime) {
  return request({
    url: `/system/consultantOrder/income/${consultantId}`,
    method: 'get',
    params: {
      startTime: startTime,
      endTime: endTime
    }
  })
}
