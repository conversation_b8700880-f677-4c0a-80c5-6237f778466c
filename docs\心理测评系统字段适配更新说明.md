# 心理测评系统字段适配更新说明

## 概述

根据后端返回的实际量表数据结构，前端进行了字段名称和数据结构的适配更新，确保前后端数据字段完全匹配。

## 字段映射对照表

### 量表基本信息字段

| 原字段名 | 新字段名 | 类型 | 说明 |
|----------|----------|------|------|
| `scaleName` | `name` | String | 量表名称 |
| `scaleCode` | `code` | String | 量表编码 |
| `instruction` | `introduction` | String | 测评说明 |
| `timeLimit` | `duration` | String | 测评时长（改为字符串格式，如"5~10分钟"） |
| `difficultyLevel` | `difficultyDesc` | String | 难度等级（直接显示描述） |
| `isFree` | `payMode` | Integer | 付费模式（0=免费，1=付费，2=部分付费） |
| - | `scoringType` | String | 计分类型（LIKERT、SUM、AVERAGE等） |
| - | `applicableAge` | String | 适用年龄 |
| - | `searchKeywords` | String | 搜索关键词 |
| - | `searchCount` | Integer | 搜索次数 |
| - | `statusDesc` | String | 状态描述 |
| - | `priceDesc` | String | 价格描述 |

### 新增字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `payPhase` | Integer | 付费阶段 |
| `freeVipLevel` | Integer | 免费VIP等级 |
| `freeReportLevel` | Integer | 免费报告层级 |
| `paidReportLevel` | Integer | 付费报告层级 |
| `normMean` | Double | 常模均值 |
| `normSd` | Double | 常模标准差 |
| `imageUrl` | String | 量表图片URL |
| `enterpriseId` | Long | 企业ID |
| `sort` | Integer | 排序 |
| `ratingCount` | Integer | 评分次数 |
| `published` | Boolean | 是否已发布 |
| `freeScale` | Boolean | 是否免费量表 |

## 前端更新内容

### 1. 查询参数更新

**文件**：`src/views/system/assessment/scale/index.vue`

**更新内容**：
```javascript
// 更新前
const queryParams = ref({
  scaleName: null,
  scaleCode: null,
  // ...
});

// 更新后
const queryParams = ref({
  name: null,
  code: null,
  // ...
});
```

### 2. 表格列字段更新

**更新内容**：
```vue
<!-- 更新前 -->
<el-table-column label="量表名称" prop="scaleName" />
<el-table-column label="量表编码" prop="scaleCode" />
<el-table-column label="时间限制" prop="timeLimit" />

<!-- 更新后 -->
<el-table-column label="量表名称" prop="name" />
<el-table-column label="量表编码" prop="code" />
<el-table-column label="测评时长" prop="duration" />
```

### 3. 表单字段更新

**更新内容**：
```vue
<!-- 更新前 -->
<el-form-item label="量表名称" prop="scaleName">
  <el-input v-model="form.scaleName" />
</el-form-item>

<!-- 更新后 -->
<el-form-item label="量表名称" prop="name">
  <el-input v-model="form.name" />
</el-form-item>
```

### 4. 详情页面字段更新

**更新内容**：
```vue
<!-- 更新前 -->
<el-descriptions-item label="量表名称">{{ detailForm.scaleName }}</el-descriptions-item>
<el-descriptions-item label="时间限制">{{ detailForm.timeLimit }}分钟</el-descriptions-item>

<!-- 更新后 -->
<el-descriptions-item label="量表名称">{{ detailForm.name }}</el-descriptions-item>
<el-descriptions-item label="测评时长">{{ detailForm.duration }}</el-descriptions-item>
```

### 5. 表单验证规则更新

**更新内容**：
```javascript
// 更新前
rules: {
  scaleName: [{ required: true, message: "量表名称不能为空", trigger: "blur" }],
  scaleCode: [{ required: true, message: "量表编码不能为空", trigger: "blur" }]
}

// 更新后
rules: {
  name: [{ required: true, message: "量表名称不能为空", trigger: "blur" }],
  code: [{ required: true, message: "量表编码不能为空", trigger: "blur" }]
}
```

### 6. 表单重置函数更新

**更新内容**：
```javascript
// 更新前
function reset() {
  form.value = {
    scaleName: null,
    scaleCode: null,
    timeLimit: null,
    isFree: 1,
    // ...
  };
}

// 更新后
function reset() {
  form.value = {
    name: null,
    code: null,
    duration: null,
    payMode: 0,
    // ...
  };
}
```

## 显示优化

### 1. 状态和价格显示

**优化前**：使用字典标签显示状态和价格
```vue
<dict-tag :options="scale_status_options" :value="scope.row.status" />
```

**优化后**：直接显示后端返回的描述文本
```vue
<span>{{ scope.row.statusDesc }}</span>
<span>{{ scope.row.priceDesc }}</span>
```

### 2. 测评说明显示

**优化前**：纯文本显示
```vue
<el-descriptions-item label="测评说明">{{ detailForm.instruction }}</el-descriptions-item>
```

**优化后**：支持HTML格式显示
```vue
<el-descriptions-item label="测评说明">
  <div v-html="detailForm.introduction"></div>
</el-descriptions-item>
```

### 3. 评分显示

**优化前**：直接显示评分
```vue
<el-rate v-model="detailForm.ratingAvg" disabled />
```

**优化后**：处理空值情况
```vue
<el-rate v-model="detailForm.ratingAvg" disabled v-if="detailForm.ratingAvg" />
<span v-else>暂无评分</span>
```

## 新增功能支持

### 1. 计分类型选择

```vue
<el-form-item label="计分类型" prop="scoringType">
  <el-select v-model="form.scoringType" placeholder="请选择计分类型">
    <el-option label="李克特量表" value="LIKERT" />
    <el-option label="求和计分" value="SUM" />
    <el-option label="平均计分" value="AVERAGE" />
  </el-select>
</el-form-item>
```

### 2. 付费模式选择

```vue
<el-form-item label="付费模式" prop="payMode">
  <el-radio-group v-model="form.payMode">
    <el-radio :value="0">免费</el-radio>
    <el-radio :value="1">付费</el-radio>
    <el-radio :value="2">部分付费</el-radio>
  </el-radio-group>
</el-form-item>
```

### 3. 适用年龄输入

```vue
<el-form-item label="适用年龄" prop="applicableAge">
  <el-input v-model="form.applicableAge" placeholder="请输入适用年龄，如：成人" />
</el-form-item>
```

## 数据结构示例

### 后端返回的量表数据结构

```json
{
  "id": 1,
  "name": "焦虑自评量表(SAS)",
  "code": "SAS",
  "categoryId": null,
  "description": "焦虑自评量表(Self-Rating Anxiety Scale,SAS)由Zung于1971年编制...",
  "introduction": "SAS是心理学专业量表，用于测量焦虑状态轻重程度...",
  "questionCount": 20,
  "scoringType": "LIKERT",
  "duration": "5~10分钟",
  "applicableAge": "成人",
  "price": 0,
  "payMode": 0,
  "status": 1,
  "searchCount": 0,
  "viewCount": 0,
  "statusDesc": "已发布",
  "priceDesc": "免费",
  "difficultyDesc": "简单",
  "published": true,
  "freeScale": true
}
```

## 兼容性处理

### 1. 字段存在性检查

```javascript
// 安全的字段访问
const displayName = detailForm.name || detailForm.scaleName || '未知';
const displayCode = detailForm.code || detailForm.scaleCode || '未知';
```

### 2. 条件渲染

```vue
<!-- 只有存在数据时才显示 -->
<el-descriptions-item label="评分" v-if="detailForm.ratingAvg">
  <el-rate v-model="detailForm.ratingAvg" disabled />
</el-descriptions-item>
```

## 测试建议

### 1. 字段显示测试

- [ ] 量表列表字段显示正确
- [ ] 量表详情字段显示完整
- [ ] 表单字段编辑正常
- [ ] 搜索条件字段匹配

### 2. 数据提交测试

- [ ] 新增量表数据提交正确
- [ ] 修改量表数据更新正常
- [ ] 字段验证规则生效
- [ ] 表单重置功能正常

### 3. 兼容性测试

- [ ] 新旧字段兼容处理
- [ ] 空值情况处理
- [ ] 异常数据容错

## 注意事项

1. **字段名称**：确保前后端字段名称完全一致
2. **数据类型**：注意字段类型变化，如duration从数字改为字符串
3. **显示格式**：利用后端返回的描述字段，简化前端显示逻辑
4. **验证规则**：根据新字段调整表单验证规则
5. **向后兼容**：考虑旧数据的兼容性处理

## 更新日志

- 2024-01-XX: 完成字段名称适配更新
- 2024-01-XX: 优化显示格式和用户体验
- 2024-01-XX: 添加新功能支持
