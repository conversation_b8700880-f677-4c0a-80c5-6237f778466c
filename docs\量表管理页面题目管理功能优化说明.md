# 量表管理页面题目管理功能优化说明

## 概述

根据您提供的题目数据结构，我对量表管理页面中的题目管理功能进行了全面优化，完善了数据显示，增加了选项查看功能，并优化了搜索和展示体验。

## 优化内容

### 1. 题目列表表格优化

#### 新增和优化的显示字段
- **题目内容**：优先显示 `content` 字段，兼容 `questionText` 字段
- **题目类型**：使用彩色标签显示，支持 `questionTypeDesc` 和类型映射
- **子量表**：新增显示 `subscaleRef` 字段
- **是否必答**：使用标签显示，支持 `requiredDesc` 和布尔值映射
- **反向计分**：新增显示 `isReverse` 字段，用标签区分
- **排序**：显示 `sort` 字段
- **选项数量**：显示 `optionList.length`，实时统计选项个数
- **查看选项**：新增操作按钮，可查看题目的详细选项信息

#### 字段映射关系

| 显示名称 | 主要字段 | 备用字段 | 数据类型 | 说明 |
|---------|---------|---------|---------|------|
| 题目内容 | content | questionText | String | 题目的具体内容 |
| 题目类型 | questionTypeDesc | questionType | String | 单选/多选/填空/判断 |
| 子量表 | subscaleRef | - | String | 子量表引用名称 |
| 是否必答 | requiredDesc | isRequired | String/Integer | 是否为必答题 |
| 反向计分 | isReverse | - | Integer | 0=否, 1=是 |
| 排序 | sort | - | Integer | 题目排序号 |
| 选项数量 | optionList.length | - | Integer | 实时计算选项个数 |

### 2. 选项查看功能

#### 新增选项详情对话框
- **题目信息卡片**：完整展示题目的基本信息
- **选项列表表格**：详细展示所有选项信息
- **响应式设计**：适配不同屏幕尺寸

#### 选项信息展示
根据您提供的数据结构，选项详情包含：

```javascript
// 选项数据结构
{
  "id": 760,
  "questionId": 277,
  "optionText": "非常同意",      // 选项文本
  "optionValue": "1",          // 选项值
  "score": null,               // 分数
  "delFlag": 0,                // 删除标志
  "displayText": "1. 非常同意", // 显示文本
  "createTime": "2025-07-19 17:59:38"
}
```

#### 选项表格字段
- **选项值**：显示 `optionValue`
- **选项文本**：显示 `optionText`
- **显示文本**：显示 `displayText`（包含序号的完整文本）
- **分数**：显示 `score`，为空时显示 "-"
- **状态**：根据 `delFlag` 显示正常/已删除状态
- **创建时间**：格式化显示创建时间

### 3. 搜索功能增强

#### 新增搜索条件
- **子量表搜索**：支持按子量表名称搜索题目
- **题目类型优化**：直接使用枚举值，不依赖数据字典

#### 搜索参数结构
```javascript
questionQueryParams: {
  pageNum: 1,
  pageSize: 10,
  scaleId: null,           // 量表ID（自动设置）
  content: null,           // 题目内容
  questionType: null,      // 题目类型 (SINGLE/MULTIPLE/FILL/JUDGE)
  subscaleRef: null        // 子量表
}
```

### 4. 数据类型处理

#### 题目类型映射
```javascript
// 题目类型文本映射
const questionTypeMap = {
  'SINGLE': '单选题',
  'MULTIPLE': '多选题', 
  'FILL': '填空题',
  'JUDGE': '判断题'
};

// 题目类型标签样式映射
const questionTypeTagMap = {
  'SINGLE': 'primary',    // 蓝色
  'MULTIPLE': 'success',  // 绿色
  'FILL': 'warning',      // 橙色
  'JUDGE': 'info'         // 灰色
};
```

#### 状态标签样式
- **必答题**：红色标签 (danger)
- **非必答题**：蓝色标签 (info)
- **反向计分**：橙色标签 (warning)
- **正常计分**：绿色标签 (success)
- **正常状态**：绿色标签 (success)
- **已删除**：红色标签 (danger)

### 5. 用户界面优化

#### 题目列表界面
- **响应式列宽**：题目内容列使用 `min-width="200"`，自适应内容长度
- **工具提示**：长文本内容支持悬停查看完整内容
- **操作按钮**：新增"查看选项"按钮，图标为 `View`
- **状态标识**：使用颜色标签直观显示各种状态

#### 选项详情界面
- **卡片布局**：题目信息和选项列表分别使用卡片展示
- **描述列表**：题目信息使用 `el-descriptions` 组件，2列布局
- **表格展示**：选项信息使用表格展示，支持排序和筛选
- **空状态处理**：无选项时显示友好的空状态提示

### 6. 技术实现

#### 模板优化
```vue
<!-- 题目内容兼容显示 -->
<el-table-column label="题目内容" align="center" :show-overflow-tooltip="true" min-width="200">
  <template #default="scope">
    {{ scope.row.content || scope.row.questionText }}
  </template>
</el-table-column>

<!-- 题目类型标签显示 -->
<el-table-column label="题目类型" align="center" width="100">
  <template #default="scope">
    <el-tag :type="getQuestionTypeTag(scope.row.questionType)">
      {{ scope.row.questionTypeDesc || getQuestionTypeText(scope.row.questionType) }}
    </el-tag>
  </template>
</el-table-column>

<!-- 选项数量实时计算 -->
<el-table-column label="选项数量" align="center" width="80">
  <template #default="scope">
    {{ scope.row.optionList ? scope.row.optionList.length : 0 }}
  </template>
</el-table-column>
```

#### 方法实现
```javascript
/** 查看题目选项 */
function handleViewQuestionOptions(row) {
  currentQuestion.value = row;
  optionDialogOpen.value = true;
}

/** 获取题目类型标签样式 */
function getQuestionTypeTag(questionType) {
  const typeMap = {
    'SINGLE': 'primary',
    'MULTIPLE': 'success', 
    'FILL': 'warning',
    'JUDGE': 'info'
  };
  return typeMap[questionType] || 'info';
}

/** 获取题目类型文本 */
function getQuestionTypeText(questionType) {
  const typeMap = {
    'SINGLE': '单选题',
    'MULTIPLE': '多选题',
    'FILL': '填空题', 
    'JUDGE': '判断题'
  };
  return typeMap[questionType] || questionType;
}
```

### 7. 数据兼容性

#### 字段兼容处理
- **题目内容**：`content` || `questionText`
- **题目类型**：`questionTypeDesc` || 映射值
- **是否必答**：`requiredDesc` || 布尔值转换
- **选项数量**：`optionList.length` || 0

#### 数据格式处理
- 支持新旧数据结构
- 自动处理字段缺失情况
- 提供默认值和容错处理

## 功能特点

### 1. 完整的题目信息展示
- ✅ 显示题目的所有重要字段
- ✅ 支持选项详情查看
- ✅ 实时计算选项数量
- ✅ 状态标签化显示

### 2. 增强的搜索功能
- ✅ 多维度搜索条件
- ✅ 子量表筛选
- ✅ 题目类型筛选
- ✅ 内容模糊搜索

### 3. 友好的用户界面
- ✅ 响应式设计
- ✅ 颜色标签区分
- ✅ 工具提示支持
- ✅ 空状态处理

### 4. 数据完整性
- ✅ 兼容多种数据格式
- ✅ 容错处理机制
- ✅ 实时数据更新

## 使用说明

### 1. 查看题目选项
1. 在量表管理页面点击"题目管理"
2. 在题目列表中点击"查看选项"按钮
3. 在弹出的对话框中查看题目详情和选项列表

### 2. 搜索题目
1. 使用题目内容进行模糊搜索
2. 按题目类型进行筛选
3. 按子量表进行筛选
4. 支持多条件组合搜索

### 3. 题目信息查看
- 题目基本信息：题号、类型、内容等
- 配置信息：子量表、必答、反向计分等
- 选项信息：选项值、文本、分数等
- 状态信息：创建时间、删除状态等

## 后续建议

### 1. 功能增强
- 添加选项编辑功能
- 支持题目预览功能
- 增加批量操作功能

### 2. 用户体验
- 添加选项排序功能
- 支持选项拖拽排序
- 增加题目复制功能

### 3. 数据分析
- 添加题目使用统计
- 选项选择频率分析
- 题目难度分析

## 总结

通过这次优化，量表管理页面中的题目管理功能现在能够：

1. **完整展示题目信息**：包括所有重要字段和选项详情
2. **提供选项查看功能**：详细展示题目的所有选项信息
3. **支持多维度搜索**：按内容、类型、子量表等条件搜索
4. **友好的用户界面**：使用标签和颜色提升视觉体验
5. **数据兼容性强**：支持新旧数据结构，容错处理完善

这个集成的题目管理功能替代了原来的单独题目管理页面，提供了更好的用户体验和更完整的功能。
