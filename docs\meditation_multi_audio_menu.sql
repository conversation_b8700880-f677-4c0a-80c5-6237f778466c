-- 冥想多音频功能菜单配置SQL脚本
-- 执行前请确认当前系统的菜单ID范围，避免ID冲突

-- 1. 添加冥想音频管理菜单
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('冥想音频管理', (SELECT menu_id FROM sys_menu WHERE menu_name = '冥想管理' LIMIT 1), 2, 'audio/:id', 'wechat/meditation/audio/index', '', 1, 0, 'C', '1', '0', 'system:meditation:audio:list', 'headphone', 'admin', NOW(), '', NULL, '冥想音频管理菜单');

-- 获取刚插入的音频管理菜单ID
SET @audio_menu_id = LAST_INSERT_ID();

-- 2. 添加冥想音频管理相关权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('冥想音频查询', @audio_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:audio:query', '#', 'admin', NOW(), '', NULL, ''),
('冥想音频新增', @audio_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:audio:add', '#', 'admin', NOW(), '', NULL, ''),
('冥想音频修改', @audio_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:audio:edit', '#', 'admin', NOW(), '', NULL, ''),
('冥想音频删除', @audio_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:audio:remove', '#', 'admin', NOW(), '', NULL, ''),
('冥想音频导出', @audio_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:audio:export', '#', 'admin', NOW(), '', NULL, '');

-- 3. 添加冥想播放记录管理菜单（如果需要独立管理播放记录）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('冥想播放记录', (SELECT menu_id FROM sys_menu WHERE menu_name = '冥想管理' LIMIT 1), 3, 'record', 'wechat/meditation/record/index', '', 1, 0, 'C', '1', '0', 'system:meditation:record:list', 'video-play', 'admin', NOW(), '', NULL, '冥想播放记录管理菜单');

-- 获取刚插入的播放记录菜单ID
SET @record_menu_id = LAST_INSERT_ID();

-- 4. 添加冥想播放记录相关权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) 
VALUES 
('播放记录查询', @record_menu_id, 1, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:record:query', '#', 'admin', NOW(), '', NULL, ''),
('播放记录新增', @record_menu_id, 2, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:record:add', '#', 'admin', NOW(), '', NULL, ''),
('播放记录修改', @record_menu_id, 3, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:record:edit', '#', 'admin', NOW(), '', NULL, ''),
('播放记录删除', @record_menu_id, 4, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:record:remove', '#', 'admin', NOW(), '', NULL, ''),
('播放记录导出', @record_menu_id, 5, '', '', '', 1, 0, 'F', '0', '0', 'system:meditation:record:export', '#', 'admin', NOW(), '', NULL, '');

-- 5. 为管理员角色分配新权限（假设管理员角色ID为1）
-- 获取新增的菜单ID并分配给管理员角色
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms IN (
    'system:meditation:audio:list',
    'system:meditation:audio:query', 
    'system:meditation:audio:add',
    'system:meditation:audio:edit',
    'system:meditation:audio:remove',
    'system:meditation:audio:export',
    'system:meditation:record:list',
    'system:meditation:record:query',
    'system:meditation:record:add', 
    'system:meditation:record:edit',
    'system:meditation:record:remove',
    'system:meditation:record:export'
);

-- 6. 创建路由配置（如果使用动态路由）
-- 注意：以下是示例配置，实际路由配置可能需要根据项目具体情况调整

-- 查询新增的菜单信息（用于验证）
SELECT 
    menu_id,
    menu_name,
    parent_id,
    order_num,
    path,
    component,
    perms,
    menu_type,
    visible,
    status
FROM sys_menu 
WHERE perms LIKE 'system:meditation:audio:%' 
   OR perms LIKE 'system:meditation:record:%'
ORDER BY parent_id, order_num;

-- 验证角色权限分配
SELECT 
    r.role_name,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.perms LIKE 'system:meditation:audio:%' 
   OR m.perms LIKE 'system:meditation:record:%'
ORDER BY r.role_id, m.order_num;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 请根据实际的菜单ID范围调整脚本
-- 3. 如果系统中已存在相关菜单，请先删除或修改现有菜单
-- 4. 角色ID请根据实际系统中的角色ID进行调整
-- 5. 路由路径和组件路径请根据实际项目结构调整
