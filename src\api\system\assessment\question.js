import request from '@/utils/request'

// 查询题目列表
export function listQuestion(query) {
  return request({
    url: '/system/question/list',
    method: 'get',
    params: query
  })
}

// 查询题目详细
export function getQuestion(id) {
  return request({
    url: '/system/question/' + id,
    method: 'get'
  })
}

// 获取题目详情（包含选项信息）
export function getQuestionDetails(id) {
  return request({
    url: '/system/question/details/' + id,
    method: 'get'
  })
}

// 新增题目
export function addQuestion(data) {
  return request({
    url: '/system/question',
    method: 'post',
    data: data
  })
}

// 修改题目
export function updateQuestion(data) {
  return request({
    url: '/system/question',
    method: 'put',
    data: data
  })
}

// 删除题目
export function delQuestion(ids) {
  return request({
    url: '/system/question/' + ids,
    method: 'delete'
  })
}

// 根据量表ID查询题目列表
export function getQuestionsByScale(scaleId) {
  return request({
    url: '/system/question/scale/' + scaleId,
    method: 'get'
  })
}

// 根据量表ID查询题目列表（包含选项）
export function getQuestionsWithOptionsByScale(scaleId) {
  return request({
    url: '/system/question/scale/' + scaleId + '/with-options',
    method: 'get'
  })
}

// 根据题目序号查询题目
export function getQuestionByNo(scaleId, questionNo) {
  return request({
    url: '/system/question/scale/' + scaleId + '/no/' + questionNo,
    method: 'get'
  })
}

// 查询题目统计信息
export function getQuestionStats(scaleId) {
  return request({
    url: '/system/question/stats/' + scaleId,
    method: 'get'
  })
}

// 复制题目
export function copyQuestions(sourceScaleId, targetScaleId) {
  return request({
    url: '/system/question/copy',
    method: 'post',
    params: {
      sourceScaleId: sourceScaleId,
      targetScaleId: targetScaleId
    }
  })
}

// 验证题目配置
export function validateQuestions(scaleId) {
  return request({
    url: '/system/question/validate/' + scaleId,
    method: 'get'
  })
}

// 自动生成题目序号
export function generateQuestionNumbers(scaleId) {
  return request({
    url: '/system/question/generate-numbers/' + scaleId,
    method: 'post'
  })
}

// 重新排序题目
export function reorderQuestions(scaleId) {
  return request({
    url: '/system/question/reorder/' + scaleId,
    method: 'post'
  })
}

// 更新题目显示顺序
export function updateQuestionOrder(id, orderNum) {
  return request({
    url: '/system/question/order',
    method: 'put',
    params: {
      id: id,
      orderNum: orderNum
    }
  })
}

// 批量更新题目状态
export function batchUpdateQuestionStatus(ids, status) {
  return request({
    url: '/system/question/status',
    method: 'put',
    params: {
      ids: ids,
      status: status
    }
  })
}

// 根据分量表查询题目
export function getQuestionsBySubscale(subscaleId) {
  return request({
    url: '/system/question/subscale/' + subscaleId,
    method: 'get'
  })
}

// 查询必答题目
export function getRequiredQuestions(scaleId) {
  return request({
    url: '/system/question/required/' + scaleId,
    method: 'get'
  })
}

// 查询选答题目
export function getOptionalQuestions(scaleId) {
  return request({
    url: '/system/question/optional/' + scaleId,
    method: 'get'
  })
}

// 随机获取题目
export function getRandomQuestions(scaleId, count) {
  return request({
    url: '/system/question/random/' + scaleId,
    method: 'get',
    params: {
      count: count
    }
  })
}

// 导入题目
export function importQuestions(scaleId, file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/system/question/import/' + scaleId,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出题目
export function exportQuestion(query) {
  return request({
    url: '/system/question/export',
    method: 'post',
    params: query
  })
}

// 导出指定量表的题目
export function exportQuestionsByScale(scaleId) {
  return request({
    url: '/system/question/export/' + scaleId,
    method: 'post',
    responseType: 'blob'
  })
}

// 获取导入模板
export function getImportTemplate() {
  return request({
    url: '/system/question/importTemplate',
    method: 'post',
    responseType: 'blob'
  })
}
