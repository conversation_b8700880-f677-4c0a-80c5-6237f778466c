import request from '@/utils/request'

// 查询量表列表
export function listScale(query) {
  return request({
    url: '/system/scale/list',
    method: 'get',
    params: query
  })
}

// 获取量表详情
export function getScale(id) {
  return request({
    url: '/system/scale/' + id,
    method: 'get'
  })
}

// 新增量表
export function addScale(data) {
  return request({
    url: '/system/scale',
    method: 'post',
    data: data
  })
}

// 修改量表
export function updateScale(data) {
  return request({
    url: '/system/scale',
    method: 'put',
    data: data
  })
}

// 删除量表
export function delScale(ids) {
  return request({
    url: '/system/scale/' + ids,
    method: 'delete'
  })
}

// 发布量表
export function publishScale(id) {
  return request({
    url: '/system/scale/' + id + '/publish',
    method: 'post'
  })
}

// 下架量表
export function unpublishScale(id) {
  return request({
    url: '/system/scale/' + id + '/unpublish',
    method: 'post'
  })
}

// 导出量表
export function exportScale(query) {
  return request({
    url: '/system/scale/export',
    method: 'post',
    params: query
  })
}

// 获取量表统计信息
export function getScaleStats(id) {
  return request({
    url: '/system/scale/stats/' + id,
    method: 'get'
  })
}

// 复制量表 (如果后端支持)
export function copyScale(id, data) {
  return request({
    url: '/system/scale/copy/' + id,
    method: 'post',
    data: data
  })
}

// 验证量表完整性 (如果后端支持)
export function validateScale(id) {
  return request({
    url: '/system/scale/validate/' + id,
    method: 'get'
  })
}
