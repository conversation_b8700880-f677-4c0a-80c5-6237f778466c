<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="咨询师" prop="counselorId">
        <el-select v-model="queryParams.counselorId" placeholder="请选择咨询师" clearable style="width: 240px">
          <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
            :value="counselor.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="排班日期" prop="scheduleDate">
        <el-date-picker v-model="queryParams.scheduleDate" type="date" placeholder="选择排班日期" clearable
          style="width: 240px" />
      </el-form-item>
      <el-form-item label="门店" prop="centerId">
        <el-select v-model="queryParams.centerId" placeholder="请选择门店" clearable style="width: 240px">
          <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:schedule:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:schedule:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:schedule:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Plus" @click="handleBatchAdd"
          v-hasPermi="['system:schedule:add']">批量新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Magic" @click="handleGenerateDefault"
          v-hasPermi="['system:schedule:add']">生成默认排班</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="scheduleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="排班ID" align="center" prop="id" width="80" />
      <el-table-column label="咨询师ID" align="center" prop="counselorId" width="100" />
      <el-table-column label="排班日期" align="center" prop="scheduleDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.scheduleDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="100" />
      <el-table-column label="结束时间" align="center" prop="endTime" width="100" />
      <el-table-column label="门店" align="center" width="120">
        <template #default="scope">
          {{ getStoreName(scope.row.centerId) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="schedule_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:schedule:edit']"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:schedule:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改排班对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="scheduleRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="咨询师ID" prop="counselorId">
              <el-input v-model="form.counselorId" placeholder="请输入咨询师ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排班日期" prop="scheduleDate">
              <el-date-picker v-model="form.scheduleDate" type="date" placeholder="选择排班日期" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker v-model="form.startTime" placeholder="选择开始时间" format="HH:mm" value-format="HH:mm"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker v-model="form.endTime" placeholder="选择结束时间" format="HH:mm" value-format="HH:mm"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="门店" prop="centerId">
              <el-select v-model="form.centerId" placeholder="请选择门店" style="width: 100%">
                <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option v-for="dict in schedule_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Schedule">
import {
  listSchedule,
  getSchedule,
  delSchedule,
  addSchedule,
  updateSchedule,
  batchAddSchedule,
  generateDefaultSchedule,
  generateAllSchedule,
  checkScheduleExists
} from "@/api/system/counselor/schedule";
import { listStore } from "@/api/wechat/store";
import { listConsultant } from "@/api/wechat/consultation/consultant";

const { proxy } = getCurrentInstance();
const { schedule_status } = proxy.useDict('schedule_status');

const scheduleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 门店列表
const storeList = ref([]);
// 咨询师列表
const counselorList = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    counselorId: null,
    scheduleDate: null,
    centerId: null
  },
  rules: {
    counselorId: [
      { required: true, message: "咨询师ID不能为空", trigger: "blur" }
    ],
    scheduleDate: [
      { required: true, message: "排班日期不能为空", trigger: "change" }
    ],
    startTime: [
      { required: true, message: "开始时间不能为空", trigger: "change" }
    ],
    endTime: [
      { required: true, message: "结束时间不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询排班列表 */
function getList() {
  loading.value = true;
  listSchedule(queryParams.value).then(response => {
    scheduleList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取门店列表 */
function getStoreList() {
  listStore().then(response => {
    storeList.value = response.rows || response.data || [];
  });
}

/** 获取门店名称 */
function getStoreName(storeId) {
  const store = storeList.value.find(s => s.id === storeId);
  return store ? store.name : `门店${storeId}`;
}

/** 获取咨询师列表 */
function getCounselorList() {
  listConsultant().then(response => {
    counselorList.value = response.rows || response.data || [];
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    counselorId: null,
    scheduleDate: null,
    startTime: null,
    endTime: null,
    centerId: 1,
    status: "0",
    remark: null
  };
  proxy.resetForm("scheduleRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加排班";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const scheduleId = row.id || ids.value[0];
  getSchedule(scheduleId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改排班";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["scheduleRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateSchedule(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSchedule(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const scheduleIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除排班编号为"' + scheduleIds + '"的数据项？').then(function () {
    return delSchedule(scheduleIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 批量新增按钮操作 */
function handleBatchAdd() {
  // 这里可以打开批量新增对话框
  proxy.$modal.msgInfo("批量新增功能待实现");
}

/** 生成默认排班按钮操作 */
function handleGenerateDefault() {
  // 这里可以打开生成默认排班对话框
  proxy.$modal.msgInfo("生成默认排班功能待实现");
}

onMounted(() => {
  getStoreList();
  getCounselorList();
  getList();
});
</script>
