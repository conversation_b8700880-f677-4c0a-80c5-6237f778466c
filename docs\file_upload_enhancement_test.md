# FileUpload组件增强测试指南

## 修改说明

我们对FileUpload组件进行了非破坏性增强，添加了一个新的事件`@file-uploaded`来传递文件信息。

### 组件修改

#### 1. FileUpload组件 (`src/components/FileUpload/index.vue`)

**新增事件定义：**
```javascript
const emit = defineEmits(['update:modelValue', 'file-uploaded']);
```

**新增事件触发：**
```javascript
// 在handleUploadSuccess方法中添加
emit('file-uploaded', {
  url: res.url,           // 服务器返回的文件URL
  fileName: res.fileName, // 服务器返回的文件名
  originalName: file.name, // 原始文件名
  size: file.size,        // 文件大小（字节）
  type: file.type         // 文件MIME类型
});
```

#### 2. 音频管理页面 (`src/views/wechat/meditation/audio/index.vue`)

**新增事件监听：**
```html
<file-upload 
  v-model="form.audioUrl" 
  :file-type="['mp3', 'wav', 'flac']" 
  @file-uploaded="handleFileUploaded" 
/>
```

**新增处理方法：**
```javascript
function handleFileUploaded(fileInfo) {
  // 直接从fileInfo获取准确的文件信息
  form.value.fileSize = fileInfo.size;
  form.value.fileFormat = fileInfo.originalName.split('.').pop().toLowerCase();
  // ... 其他处理逻辑
}
```

## 测试步骤

### 1. 基本功能测试

1. **打开音频管理页面**
   - 访问：`/wechat/meditation/audio/:id`
   - 点击"新增音频"按钮

2. **上传音频文件**
   - 点击"选取文件"按钮
   - 选择一个音频文件（mp3、wav或flac）
   - 观察上传过程

3. **验证文件信息获取**
   - 检查控制台是否输出：`文件上传成功: {url, fileName, originalName, size, type}`
   - 验证以下字段是否自动填充：
     - 文件格式：显示正确的扩展名
     - 文件大小：显示格式化的大小（如"3.2 MB"）
     - 时长：显示音频时长（秒）
     - 比特率和采样率：显示默认值

### 2. 文件信息准确性测试

#### 测试用例1：小MP3文件
```
文件：test.mp3
大小：2.5 MB
时长：3分15秒
预期结果：
- 文件格式：mp3
- 文件大小：2.5 MB
- 时长：195秒
- 比特率：128 kbps
- 采样率：44100 Hz
```

#### 测试用例2：WAV文件
```
文件：audio.wav
大小：45 MB
时长：4分30秒
预期结果：
- 文件格式：wav
- 文件大小：45 MB
- 时长：270秒
- 比特率：1411 kbps
- 采样率：44100 Hz
```

#### 测试用例3：FLAC文件
```
文件：music.flac
大小：28 MB
时长：5分钟
预期结果：
- 文件格式：flac
- 文件大小：28 MB
- 时长：300秒
- 比特率：1000 kbps
- 采样率：44100 Hz
```

### 3. 兼容性测试

**验证其他使用FileUpload组件的页面不受影响：**

1. **图片上传页面**
   - 测试图片上传功能正常
   - 确认没有额外的事件触发

2. **文档上传页面**
   - 测试文档上传功能正常
   - 确认现有功能不受影响

3. **其他文件上传场景**
   - 批量上传功能
   - 文件删除功能
   - 文件预览功能

### 4. 错误处理测试

1. **上传失败场景**
   - 上传失败时不应触发`@file-uploaded`事件
   - 错误信息正常显示

2. **网络异常场景**
   - 网络中断时的处理
   - 超时情况的处理

3. **文件格式错误**
   - 上传不支持的格式
   - 文件大小超限

## 预期结果

### 成功场景的控制台输出
```javascript
// 文件上传成功时
文件上传成功: {
  url: "http://localhost:8080/profile/upload/2024/01/15/audio_20240115123456.mp3",
  fileName: "audio_20240115123456.mp3",
  originalName: "我的音频.mp3",
  size: 2621440,  // 2.5MB
  type: "audio/mpeg"
}

// 音频时长获取成功时
音频时长获取成功: 195 // 3分15秒
```

### 界面显示效果
```
文件格式: mp3
文件大小: 2.5 MB
时长(秒): 195
时长显示: 3:15
比特率: 128 kbps
采样率: 44100 Hz
```

## 优势对比

### 之前的实现问题
- 无法获取准确的文件大小
- 需要通过网络请求获取文件信息
- 依赖URL解析获取文件格式
- 可能存在跨域问题

### 现在的实现优势
- ✅ 直接从上传组件获取准确的文件大小
- ✅ 获取原始文件名，格式识别更准确
- ✅ 不需要额外的网络请求
- ✅ 避免跨域问题
- ✅ 不破坏现有组件的兼容性
- ✅ 性能更好，响应更快

## 技术细节

### 事件数据结构
```javascript
{
  url: string,           // 服务器返回的文件访问URL
  fileName: string,      // 服务器生成的文件名
  originalName: string,  // 用户上传的原始文件名
  size: number,         // 文件大小（字节）
  type: string          // 文件MIME类型
}
```

### 文件大小格式化
```javascript
function formatFileSize(bytes) {
  if (!bytes || bytes <= 0) return '0 B';
  
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(unitIndex === 0 ? 0 : 2)} ${units[unitIndex]}`;
}
```

### 音频时长获取
```javascript
function getAudioDuration(fileUrl) {
  return new Promise((resolve) => {
    const audio = new Audio();
    audio.crossOrigin = "anonymous";
    
    audio.onloadedmetadata = () => {
      if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
        resolve(audio.duration);
      } else {
        resolve(0);
      }
    };
    
    audio.onerror = () => resolve(0);
    setTimeout(() => resolve(0), 5000); // 5秒超时
    
    audio.src = fileUrl;
  });
}
```

## 注意事项

1. **向后兼容**：新增的`@file-uploaded`事件是可选的，不会影响现有功能
2. **性能优化**：直接从上传组件获取文件信息，避免额外的网络请求
3. **错误处理**：保持原有的错误处理机制不变
4. **类型安全**：确保传递的文件信息类型正确

## 故障排除

### 1. 事件未触发
- 检查组件版本是否更新
- 确认事件名称拼写正确
- 查看控制台是否有错误信息

### 2. 文件信息不准确
- 检查浏览器兼容性
- 确认文件上传成功
- 查看服务器返回的响应数据

### 3. 时长获取失败
- 检查音频文件是否损坏
- 确认文件URL可访问
- 查看网络连接状态

通过这个增强，我们实现了更准确、更高效的文件信息获取，同时保持了组件的向后兼容性。
