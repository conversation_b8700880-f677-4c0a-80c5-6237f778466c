<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入模板名称" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="咨询师" prop="counselorId">
        <el-select v-model="queryParams.counselorId" placeholder="请选择咨询师" clearable style="width: 200px">
          <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
            :value="counselor.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否默认" prop="isDefault">
        <el-select v-model="queryParams.isDefault" placeholder="请选择" clearable style="width: 120px">
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:template:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:template:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:template:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="DocumentCopy" :disabled="single" @click="handleCopy"
          v-hasPermi="['system:template:add']">复制</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Setting" :disabled="single" @click="handleSetDefault"
          v-hasPermi="['system:template:edit']">设为默认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:template:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模板ID" align="center" prop="id" width="80" />
      <el-table-column label="模板名称" align="center" prop="name" :show-overflow-tooltip="true" />
      <el-table-column label="咨询师ID" align="center" prop="counselorId" width="100" />
      <el-table-column label="咨询师" align="center" width="120">
        <template #default="scope">
          {{ getCounselorName(scope.row.counselorId) }}
        </template>
      </el-table-column>
      <el-table-column label="是否默认" align="center" prop="isDefault" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.isDefault === 1 ? 'success' : 'info'">
            {{ scope.row.isDefault === 1 ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="生效开始" align="center" prop="effectiveStart" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.effectiveStart, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="生效结束" align="center" prop="effectiveEnd" width="120">
        <template #default="scope">
          <span>{{ scope.row.effectiveEnd ? parseTime(scope.row.effectiveEnd, '{y}-{m}-{d}') : '永久' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="门店" align="center" width="120">
        <template #default="scope">
          {{ getStoreName(scope.row.centerId) }}
        </template>
      </el-table-column> -->
      <el-table-column label="模板项目数" align="center" width="100">
        <template #default="scope">
          <span>{{ scope.row.templateItems ? scope.row.templateItems.length : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-tooltip content="修改" placement="top">
            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:template:edit']"></el-button>
          </el-tooltip>
          <el-tooltip content="复制" placement="top">
            <el-button link type="success" icon="DocumentCopy" @click="handleCopy(scope.row)"
              v-hasPermi="['system:template:add']"></el-button>
          </el-tooltip>
          <el-tooltip content="设为默认" placement="top" v-if="scope.row.isDefault !== 1">
            <el-button link type="warning" icon="Setting" @click="handleSetDefault(scope.row)"
              v-hasPermi="['system:template:edit']"></el-button>
          </el-tooltip>
          <el-tooltip content="删除" placement="top">
            <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)"
              v-hasPermi="['system:template:remove']"></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改排班模板对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="templateRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="咨询师" prop="counselorId">
              <el-select v-model="form.counselorId" placeholder="请选择咨询师" style="width: 100%">
                <el-option v-for="counselor in counselorList" :key="counselor.id" :label="counselor.name"
                  :value="counselor.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="生效开始" prop="effectiveStart">
              <el-date-picker v-model="form.effectiveStart" type="date" placeholder="选择生效开始日期" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束" prop="effectiveEnd">
              <el-date-picker v-model="form.effectiveEnd" type="date" placeholder="选择生效结束日期（可选）" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否默认" prop="isDefault">
              <el-radio-group v-model="form.isDefault">
                <el-radio :value="1">是</el-radio>
                <el-radio :value="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="门店" prop="centerId">
              <el-select v-model="form.centerId" placeholder="请选择门店" style="width: 100%">
                <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" :rows="3" />
        </el-form-item>

        <!-- 模板项目配置 -->
        <el-divider content-position="left">排班时间配置</el-divider>
        <div class="template-items">
          <div v-for="(item, index) in form.templateItems" :key="index" class="template-item">
            <el-row :gutter="10">
              <el-col :span="4">
                <el-form-item :label="`${getDayName(item.dayOfWeek)}`">
                  <el-checkbox v-model="item.enabled" @change="handleDayToggle(item, index)">启用</el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开始时间">
                  <el-time-picker v-model="item.startTime" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="开始时间"
                    style="width: 100%" :disabled="!item.enabled" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="结束时间">
                  <el-time-picker v-model="item.endTime" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="结束时间"
                    style="width: 100%" :disabled="!item.enabled" />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="5">
                <el-form-item label="门店">
                  <el-select v-model="item.centerId" placeholder="选择门店" style="width: 100%" :disabled="!item.enabled">
                    <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="4">
                <el-button type="primary" size="small" @click="copyToAll(item)"
                  :disabled="!item.enabled">复制到全部</el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ScheduleTemplate">
import {
  listTemplate, getTemplate, delTemplate, addTemplate, updateTemplate,
  exportTemplate, setDefaultTemplate, copyTemplate, createDefaultTemplate,
  checkTemplateNameUnique, getTemplatesByCounselorId, getDefaultTemplate
} from "@/api/system/schedule/template";
import { listConsultant } from "@/api/wechat/consultation/consultant";
import { listStore } from "@/api/wechat/store";

const { proxy } = getCurrentInstance();

const templateList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 咨询师列表
const counselorList = ref([]);
// 门店列表
const storeList = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    counselorId: null,
    isDefault: null
  },
  rules: {
    name: [
      { required: true, message: "模板名称不能为空", trigger: "blur" }
    ],
    counselorId: [
      { required: true, message: "咨询师不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询排班模板列表 */
function getList() {
  loading.value = true;
  listTemplate(queryParams.value).then(response => {
    templateList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取咨询师名称 */
function getCounselorName(counselorId) {
  const counselor = counselorList.value.find(c => c.id === counselorId);
  return counselor ? counselor.name : `咨询师${counselorId}`;
}

/** 获取门店名称 */
// function getStoreName(storeId) {
//   const store = storeList.value.find(s => s.id === storeId);
//   return store ? store.name : `门店${storeId}`;
// }

/** 获取咨询师列表 */
function getCounselorList() {
  listConsultant().then(response => {
    counselorList.value = response.rows || response.data || [];
  });
}

/** 获取门店列表 */
function getStoreList() {
  listStore().then(response => {
    storeList.value = response.rows || response.data || [];
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    counselorId: null,
    isDefault: 0,
    effectiveStart: null,
    effectiveEnd: null,
    centerId: 1,
    remark: null,
    templateItems: initTemplateItems()
  };
  proxy.resetForm("templateRef");
}

// 初始化模板项目（一周7天）
function initTemplateItems() {
  const items = [];
  for (let i = 1; i <= 7; i++) {
    items.push({
      id: null,
      templateId: null,
      dayOfWeek: i,
      startTime: "09:00:00",
      endTime: "18:00:00",
      centerId: 1,
      enabled: false
    });
  }
  return items;
}

// 获取星期名称
function getDayName(dayOfWeek) {
  const days = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  return days[dayOfWeek] || '';
}

// 处理日期启用/禁用
function handleDayToggle(item, index) {
  if (!item.enabled) {
    item.startTime = "09:00:00";
    item.endTime = "18:00:00";
  }
}

// 复制时间到全部启用的天
function copyToAll(sourceItem) {
  form.value.templateItems.forEach(item => {
    if (item.enabled && item.dayOfWeek !== sourceItem.dayOfWeek) {
      item.startTime = sourceItem.startTime;
      item.endTime = sourceItem.endTime;
      item.centerId = sourceItem.centerId;
    }
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加排班模板";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value[0];
  getTemplate(id).then(response => {
    const data = response.data;
    form.value = {
      ...data,
      templateItems: data.templateItems || initTemplateItems()
    };

    // 为模板项目添加enabled属性
    form.value.templateItems.forEach(item => {
      item.enabled = true; // 从后端获取的数据默认都是启用的
    });

    open.value = true;
    title.value = "修改排班模板";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["templateRef"].validate(valid => {
    if (valid) {
      // 过滤出启用的模板项目
      const enabledItems = form.value.templateItems.filter(item => item.enabled);
      const submitData = {
        ...form.value,
        templateItems: enabledItems.map(item => ({
          id: item.id,
          templateId: form.value.id,
          dayOfWeek: item.dayOfWeek,
          startTime: item.startTime,
          endTime: item.endTime,
          centerId: item.centerId
        }))
      };

      if (form.value.id != null) {
        updateTemplate(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTemplate(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const templateIds = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除排班模板编号为"' + templateIds + '"的数据项？').then(function () {
    return delTemplate(templateIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 复制按钮操作 */
function handleCopy(row) {
  const templateId = row?.id || ids.value[0];
  proxy.$modal.prompt('请输入新模板名称', '复制模板', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /\S/,
    inputErrorMessage: '模板名称不能为空'
  }).then(({ value }) => {
    copyTemplate(templateId, value).then(response => {
      proxy.$modal.msgSuccess("复制成功");
      getList();
    });
  }).catch(() => { });
}

/** 设为默认按钮操作 */
function handleSetDefault(row) {
  const templateId = row?.id || ids.value[0];
  const counselorId = row?.counselorId;
  proxy.$modal.confirm(`确认将此模板设为咨询师${counselorId}的默认模板吗？`, '设置默认模板', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    setDefaultTemplate(counselorId, templateId).then(response => {
      proxy.$modal.msgSuccess("设置成功");
      getList();
    });
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/schedule/template/export', {
    ...queryParams.value
  }, `template_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getCounselorList();
  getStoreList();
  getList();
});
</script>

<style scoped>
.template-items {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.template-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.template-item:last-child {
  margin-bottom: 0;
}

.template-item .el-form-item {
  margin-bottom: 0;
}
</style>
