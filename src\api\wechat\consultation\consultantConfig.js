import request from '@/utils/request'

// 查询咨询师配置列表
export function listConsultantConfig(query) {
  return request({
    url: '/system/consultant/config/list',
    method: 'get',
    params: query
  })
}

// 查询咨询师配置详细
export function getConsultantConfig(id) {
  return request({
    url: `/system/consultant/config/${id}`,
    method: 'get'
  })
}

// 根据咨询师ID查询配置
export function getConsultantConfigByConsultantId(consultantId) {
  return request({
    url: `/system/consultant/config/consultant/${consultantId}`,
    method: 'get'
  })
}

// 新增咨询师配置
export function addConsultantConfig(data) {
  return request({
    url: '/system/consultant/config',
    method: 'post',
    data: data
  })
}

// 修改咨询师配置
export function updateConsultantConfig(data) {
  return request({
    url: '/system/consultant/config',
    method: 'put',
    data: data
  })
}

// 删除咨询师配置
export function delConsultantConfig(ids) {
  return request({
    url: `/system/consultant/config/${ids}`,
    method: 'delete'
  })
}

// 设置咨询师到店时间
export function setArrivalTime(consultantId, centerId, hours) {
  return request({
    url: `/system/consultant/config/arrivalTime/${consultantId}/${centerId}`,
    method: 'put',
    params: { hours }
  })
}

// 设置咨询师是否启用到店时间过滤
export function setArrivalFilterEnabled(consultantId, centerId, enabled) {
  return request({
    url: `/system/consultant/config/arrivalFilter/${consultantId}/${centerId}`,
    method: 'put',
    params: { enabled }
  })
}

// 获取咨询师到店时间
export function getArrivalTime(consultantId, centerId) {
  return request({
    url: `/system/consultant/config/arrivalTime/${consultantId}/${centerId}`,
    method: 'get'
  })
}

// 检查咨询师是否启用到店时间过滤
export function isArrivalFilterEnabled(consultantId, centerId) {
  return request({
    url: `/system/consultant/config/arrivalFilter/${consultantId}/${centerId}`,
    method: 'get'
  })
}

// 为咨询师创建默认配置
export function createDefaultConfig(consultantId, centerId) {
  return request({
    url: `/system/consultant/config/createDefault/${consultantId}/${centerId}`,
    method: 'post'
  })
}

// 批量为咨询师创建默认配置
export function batchCreateDefaultConfig(centerId, consultantIds) {
  return request({
    url: `/system/consultant/config/batchCreateDefault/${centerId}`,
    method: 'post',
    data: consultantIds
  })
}

// 根据咨询师ID和门店ID查询配置
export function getConfigByConsultantAndCenter(consultantId, centerId) {
  return request({
    url: `/system/consultant/config/consultant/${consultantId}/center/${centerId}`,
    method: 'get'
  })
}

// 更新咨询师配置状态
export function updateConsultantConfigStatus(id, status) {
  return request({
    url: '/system/consultant/config/status',
    method: 'put',
    data: {
      id: id,
      status: status
    }
  })
}
