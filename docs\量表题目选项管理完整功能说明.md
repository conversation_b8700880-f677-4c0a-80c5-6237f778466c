# 量表题目选项管理完整功能说明

## 概述

基于您提供的题目数据结构和选项列表为空的问题，我已经完善了量表管理页面中的题目管理功能，不仅能查看选项，还能完整地编辑选项，包括新增、修改、删除选项等操作。

## 功能特性

### 1. 题目列表完整显示

#### 数据字典支持
- **题目类型**：使用 `question_type_options` 字典显示
- **是否必答**：使用 `yes_no_options` 字典显示  
- **反向计分**：使用 `yes_no_options` 字典显示

#### 完整字段展示
- ✅ 题目内容（兼容 `content` 和 `questionText`）
- ✅ 题目类型（使用字典标签显示）
- ✅ 子量表（`subscaleRef`）
- ✅ 是否必答（字典标签显示）
- ✅ 反向计分（字典标签显示）
- ✅ 排序（`sort`）
- ✅ 选项数量（实时计算）

### 2. 选项查看功能

#### 智能数据加载
```javascript
function handleViewQuestionOptions(row) {
  currentQuestion.value = row;
  // 如果选项列表为空，从后端获取
  if (!row.optionList || row.optionList.length === 0) {
    getOptionsByQuestion(row.id).then(response => {
      currentQuestion.value.optionList = response.data || [];
      optionDialogOpen.value = true;
    });
  } else {
    optionDialogOpen.value = true;
  }
}
```

#### 选项详情展示
根据您提供的数据结构，完整展示选项信息：
- **选项值**：`optionValue`（如："1", "2", "3"）
- **选项文本**：`optionText`（如："非常同意", "同意"）
- **显示文本**：`displayText`（如："1. 非常同意"）
- **分数**：`score`（计分值）
- **状态**：`delFlag`（0=正常，1=已删除）
- **创建时间**：格式化显示

### 3. 选项编辑功能

#### 完整的CRUD操作
- ✅ **新增选项**：支持添加新的选项
- ✅ **编辑选项**：修改现有选项的所有属性
- ✅ **删除选项**：删除不需要的选项
- ✅ **实时更新**：操作后立即刷新显示

#### 选项编辑表单
```vue
<el-dialog :title="optionTitle" v-model="optionFormOpen" width="600px">
  <el-form ref="optionFormRef" :model="optionForm" :rules="optionRules">
    <el-form-item label="选项值" prop="optionValue">
      <el-input v-model="optionForm.optionValue" placeholder="请输入选项值" />
    </el-form-item>
    <el-form-item label="选项文本" prop="optionText">
      <el-input v-model="optionForm.optionText" placeholder="请输入选项文本" />
    </el-form-item>
    <el-form-item label="显示文本" prop="displayText">
      <el-input v-model="optionForm.displayText" placeholder="请输入显示文本（如：1. 非常同意）" />
    </el-form-item>
    <el-form-item label="分数" prop="score">
      <el-input-number v-model="optionForm.score" :min="0" :max="100" />
    </el-form-item>
    <el-form-item label="状态" prop="delFlag">
      <el-radio-group v-model="optionForm.delFlag">
        <el-radio :value="0">正常</el-radio>
        <el-radio :value="1">已删除</el-radio>
      </el-radio-group>
    </el-form-item>
  </el-form>
</el-dialog>
```

#### 表单验证规则
```javascript
const optionRules = ref({
  optionText: [
    { required: true, message: "选项文本不能为空", trigger: "blur" }
  ],
  optionValue: [
    { required: true, message: "选项值不能为空", trigger: "blur" }
  ]
});
```

### 4. API接口集成

#### 选项管理API
```javascript
import { addOption, updateOption, delOption, getOptionsByQuestion } from "@/api/system/assessment/option";
```

#### 核心方法实现
```javascript
/** 新增选项 */
function handleAddOption() {
  resetOptionForm();
  optionForm.value.questionId = currentQuestion.value.id;
  optionTitle.value = "新增选项";
  optionFormOpen.value = true;
}

/** 编辑选项 */
function handleEditOption(row) {
  resetOptionForm();
  optionForm.value = { ...row };
  optionTitle.value = "编辑选项";
  optionFormOpen.value = true;
}

/** 删除选项 */
function handleDeleteOption(row) {
  proxy.$modal.confirm('是否确认删除该选项？').then(function () {
    return delOption(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功");
    refreshCurrentQuestionOptions();
  }).catch(() => { });
}

/** 提交选项表单 */
function submitOptionForm() {
  proxy.$refs["optionFormRef"].validate(valid => {
    if (valid) {
      if (optionForm.value.id != null) {
        updateOption(optionForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          optionFormOpen.value = false;
          refreshCurrentQuestionOptions();
        });
      } else {
        addOption(optionForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          optionFormOpen.value = false;
          refreshCurrentQuestionOptions();
        });
      }
    }
  });
}
```

### 5. 数据同步机制

#### 实时更新选项列表
```javascript
/** 刷新当前题目的选项列表 */
function refreshCurrentQuestionOptions() {
  if (currentQuestion.value.id) {
    getOptionsByQuestion(currentQuestion.value.id).then(response => {
      currentQuestion.value.optionList = response.data || [];
      // 同时更新题目列表中的选项数量
      const questionIndex = questionList.value.findIndex(q => q.id === currentQuestion.value.id);
      if (questionIndex !== -1) {
        questionList.value[questionIndex].optionList = response.data || [];
      }
    });
  }
}
```

#### 选项数量实时显示
```vue
<el-table-column label="选项数量" align="center" width="80">
  <template #default="scope">
    {{ scope.row.optionList ? scope.row.optionList.length : 0 }}
  </template>
</el-table-column>
```

### 6. 用户界面优化

#### 选项列表界面
- **头部操作**：新增选项按钮
- **表格展示**：完整的选项信息表格
- **行内操作**：编辑、删除按钮
- **空状态处理**：无选项时显示友好提示和新增按钮

#### 操作权限控制
```vue
<el-button type="primary" size="small" icon="Plus" @click="handleAddOption"
  v-hasPermi="['system:assessment:question:edit']">新增选项</el-button>

<el-button link type="primary" size="small" @click="handleEditOption(scope.row)"
  v-hasPermi="['system:assessment:question:edit']">编辑</el-button>

<el-button link type="danger" size="small" @click="handleDeleteOption(scope.row)"
  v-hasPermi="['system:assessment:question:remove']">删除</el-button>
```

### 7. 数据结构支持

#### 完整支持您提供的数据结构
```javascript
// 题目数据结构
{
  "id": 277,
  "scaleId": 8,
  "questionNo": 1,
  "content": "我不喜欢参加小组讨论",
  "questionType": "SINGLE",
  "isReverse": 0,
  "subscaleRef": "Group",
  "isRequired": 1,
  "sort": 0,
  "delFlag": "0",
  "optionList": [...]
}

// 选项数据结构
{
  "id": 760,
  "questionId": 277,
  "optionText": "非常同意",
  "optionValue": "1",
  "score": null,
  "delFlag": 0,
  "displayText": "1. 非常同意",
  "createTime": "2025-07-19 17:59:38"
}
```

## 解决的问题

### 1. 选项列表为空问题
- ✅ 智能检测选项列表是否为空
- ✅ 自动从后端获取选项数据
- ✅ 提供新增选项的入口

### 2. 字典数据显示问题
- ✅ 恢复 `question_type_options` 字典的使用
- ✅ 使用 `dict-tag` 组件统一显示样式
- ✅ 支持字典数据的标签化展示

### 3. 选项编辑功能缺失
- ✅ 完整的选项CRUD操作
- ✅ 表单验证和错误处理
- ✅ 实时数据同步更新

## 使用说明

### 1. 查看题目选项
1. 在量表管理页面点击"题目管理"
2. 在题目列表中点击"查看选项"按钮
3. 在弹出的对话框中查看题目信息和选项列表

### 2. 编辑选项
1. 在选项查看对话框中点击"新增选项"或"编辑"按钮
2. 在选项编辑表单中填写或修改选项信息
3. 点击"确定"保存更改

### 3. 删除选项
1. 在选项列表中点击"删除"按钮
2. 确认删除操作
3. 选项将被删除并刷新列表

## 技术特点

### 1. 数据兼容性
- 支持新旧数据结构
- 智能字段映射
- 容错处理机制

### 2. 用户体验
- 实时数据更新
- 友好的空状态处理
- 完善的操作反馈

### 3. 权限控制
- 细粒度的功能权限
- 按钮级别的权限控制
- 安全的操作验证

### 4. 性能优化
- 按需加载选项数据
- 智能缓存机制
- 最小化API调用

## 总结

现在的题目管理功能已经完全解决了选项列表为空的问题，并提供了完整的选项编辑功能：

1. **完整的数据展示**：支持您提供的所有数据字段
2. **智能数据加载**：自动处理选项列表为空的情况
3. **完整的编辑功能**：新增、修改、删除选项
4. **实时数据同步**：操作后立即更新显示
5. **友好的用户界面**：直观的操作流程和反馈

这个集成的题目和选项管理功能现在可以完全替代单独的题目管理页面，提供了更好的用户体验和更完整的功能。
