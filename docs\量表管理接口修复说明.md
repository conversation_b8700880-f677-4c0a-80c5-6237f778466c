# 量表管理接口修复说明

## 问题描述

在点击量表管理页面时出现以下错误：
```
SyntaxError: The requested module '/src/api/system/assessment/scale.js?t=1753111585281' does not provide an export named 'offlineScale' (at index.vue:533:158)
```

## 问题原因

在更新量表管理API时，将下架量表的函数名从 `offlineScale` 重命名为 `unpublishScale`，但量表管理页面中的导入和调用没有同步更新。

## 修复内容

### 1. API文件 (`src/api/system/assessment/scale.js`)
- ✅ 已正确导出 `unpublishScale` 函数
- ✅ 接口路径已更新为 `POST /system/scale/{id}/unpublish`

### 2. 页面文件 (`src/views/system/assessment/scale/index.vue`)

#### 修复前：
```javascript
// 导入语句
import { listScale, getScale, delScale, addScale, updateScale, exportScale, publishScale, offlineScale, getScaleStats } from "@/api/system/assessment/scale";

// 函数调用
function handleOffline(row) {
  proxy.$modal.confirm('是否确认下架量表"' + row.scaleName + '"？').then(function () {
    return offlineScale(row.id);  // ❌ 使用旧函数名
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("下架成功");
  }).catch(() => { });
}
```

#### 修复后：
```javascript
// 导入语句
import { listScale, getScale, delScale, addScale, updateScale, exportScale, publishScale, unpublishScale, getScaleStats } from "@/api/system/assessment/scale";

// 函数调用
function handleOffline(row) {
  proxy.$modal.confirm('是否确认下架量表"' + row.scaleName + '"？').then(function () {
    return unpublishScale(row.id);  // ✅ 使用新函数名
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("下架成功");
  }).catch(() => { });
}
```

## 修复结果

- ✅ 修复了导入语句中的函数名
- ✅ 修复了函数调用中的函数名
- ✅ 保持了原有的功能逻辑不变
- ✅ 错误已解决，量表管理页面可以正常访问

## 验证方法

1. 重新启动开发服务器
2. 访问量表管理页面
3. 测试下架量表功能是否正常工作

## 相关文件

- `src/api/system/assessment/scale.js` - API接口文件
- `src/views/system/assessment/scale/index.vue` - 量表管理页面
- `docs/测评系统API接口更新说明.md` - 完整更新说明

---

**修复时间**：2025-01-21  
**问题状态**：✅ 已解决
