<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入咨询师姓名" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="工作状态" prop="workStatus">
        <el-select v-model="queryParams.workStatus" placeholder="工作状态" clearable style="width: 240px">
          <el-option v-for="dict in psy_work_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="审核状态" clearable style="width: 240px">
          <el-option v-for="dict in psy_audit_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="价格区间">
        <el-input-number v-model="queryParams.minPrice" :min="0" placeholder="最低价" style="width: 120px" />
        <span class="el-range-separator">-</span>
        <el-input-number v-model="queryParams.maxPrice" :min="0" placeholder="最高价" style="width: 120px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:consultant:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:consultant:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:consultant:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:consultant:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="consultantList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="咨询师ID" align="center" prop="id" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="头像" align="center" prop="imageUrl" width="100">
        <template #default="scope">
          <el-avatar :size="40" :src="scope.row.imageUrl" />
        </template>
      </el-table-column>
      <el-table-column label="工作状态" align="center" prop="workStatus">
        <template #default="scope">
          <el-switch v-model="scope.row.workStatus" active-value="0" inactive-value="1"
            @change="handleStatusChange(scope.row)" :active-text="scope.row.workStatus === '0' ? '在线' : '离线'" />
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="auditStatus">
        <template #default="scope">
          <el-tag :type="scope.row.auditStatus === '1' ? 'success' : scope.row.auditStatus === '2' ? 'danger' : 'info'">
            {{ scope.row.auditStatus === '1' ? '已通过' : scope.row.auditStatus === '2' ? '已拒绝' : '待审核' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="咨询价格" align="center" prop="price">
        <template #default="scope">
          {{ scope.row.price }} 元/小时
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:consultant:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:consultant:remove']">删除</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)" trigger="click" :teleported="true">
            <el-button link type="primary">
              <el-icon><more-filled /></el-icon>更多
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <div v-hasPermi="['system:consultant:audit']">
                  <el-dropdown-item command="audit" tabindex="0">审核</el-dropdown-item>
                </div>
                <div v-hasPermi="['system:consultant:config']">
                  <el-dropdown-item command="config" tabindex="0">配置</el-dropdown-item>
                </div>
                <div v-hasPermi="['system:consultant:query']">
                  <el-dropdown-item command="detail" tabindex="0">详情</el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" /> -->

    <!-- 咨询师配置对话框 -->
    <el-dialog :title="configTitle" v-model="configOpen" width="800px" append-to-body>
      <div class="config-header">
        <p>为咨询师配置不同门店的到店时间和过滤设置</p>
        <el-button type="primary" size="small" @click="createAllDefaultConfigs">
          为所有门店创建默认配置
        </el-button>
      </div>

      <el-form ref="configRef" :model="configForm" label-width="120px">
        <el-table :data="configForm.configs" style="width: 100%">
          <el-table-column label="门店名称" align="center" width="200">
            <template #default="scope">
              {{ getStoreName(scope.row.centerId) }}
            </template>
          </el-table-column>
          <el-table-column label="到店时间(小时)" align="center" width="150">
            <template #default="scope">
              <el-input-number v-model="scope.row.arrivalTimeHours" :min="0" :max="24" :step="0.5" placeholder="小时"
                style="width: 100%" @change="handleArrivalTimeChange(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="启用过滤" align="center" width="100">
            <template #default="scope">
              <el-switch v-model="scope.row.enableArrivalFilter" :active-value="1" :inactive-value="0"
                @change="handleFilterChange(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button v-if="!scope.row.id" type="primary" size="small" @click="createConfig(scope.row)">
                创建配置
              </el-button>
              <el-button v-else type="success" size="small" @click="updateConfig(scope.row)">
                更新配置
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitConfig">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Consultant">
import { listConsultant, getConsultant, delConsultant, updateConsultantWorkStatus, updateConsultantAuditStatus } from "@/api/wechat/consultation/consultant";
import {
  listConsultantConfig,
  addConsultantConfig,
  updateConsultantConfig,
  setArrivalTime,
  setArrivalFilterEnabled,
  getConfigByConsultantAndCenter,
  createDefaultConfig
} from "@/api/wechat/consultation/consultantConfig";
import { listStore } from "@/api/wechat/store";
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const { proxy } = getCurrentInstance();
const { psy_work_status, psy_audit_status } = proxy.useDict("psy_work_status", "psy_audit_status");

// 遮罩层
const loading = ref(false);
// 选中数组
const ids = ref([]);
// 非单个禁用
const single = ref(true);
// 非多个禁用
const multiple = ref(true);
// 显示搜索条件
const showSearch = ref(true);
// 总条数
// const total = ref(0);
// 咨询师表格数据
const consultantList = ref([]);

// 配置相关数据
const configOpen = ref(false);
const configTitle = ref("咨询师配置");
const currentConsultant = ref({});
const storeList = ref([]);
const configForm = reactive({
  configs: []
});

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  workStatus: undefined,
  auditStatus: undefined,
  minPrice: undefined,
  maxPrice: undefined
});

/** 查询咨询师列表 */
function getList() {
  loading.value = true;
  listConsultant(queryParams).then(response => {
    consultantList.value = response.data;
    // total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  router.push('/wechat/group-order-classification/consultant/detail');
}

/** 修改按钮操作 */
function handleUpdate(row) {
  router.push({
    path: '/wechat/group-order-classification/consultant/detail/' + row.id
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const consultantIds = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除所选咨询师数据?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return delConsultant(consultantIds);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/consultant/export", {
    ...queryParams
  }, "consultant_" + new Date().getTime() + ".xlsx");
}

/** 工作状态修改 */
function handleStatusChange(row) {
  let text = row.workStatus === "0" ? "启用" : "停用";
  ElMessageBox.confirm('确认要"' + text + '""' + row.name + '"的工作状态吗?', "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    return updateConsultantWorkStatus(row.id, row.workStatus);
  }).then(() => {
    ElMessage.success(text + "成功");
  }).catch(() => {
    row.workStatus = row.workStatus === "0" ? "1" : "0";
  });
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case "audit":
      handleAudit(row);
      break;
    case "config":
      handleConfig(row);
      break;
    case "detail":
      router.push('/wechat/group-order-classification/consultant/detail/' + row.id);
      break;
  }
}

/** 审核操作 */
function handleAudit(row) {
  ElMessageBox.prompt('请输入审核意见', '审核', {
    confirmButtonText: '通过',
    cancelButtonText: '拒绝',
    inputPattern: /^.{1,200}$/,
    inputErrorMessage: '审核意见不能为空且不超过200字'
  }).then(({ value }) => {
    return updateConsultantAuditStatus(row.id, '1', value);
  }).then(() => {
    getList();
    ElMessage.success("审核通过");
  }).catch((action) => {
    if (action === 'cancel') {
      ElMessageBox.prompt('请输入拒绝原因', '拒绝原因', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^.{1,200}$/,
        inputErrorMessage: '拒绝原因不能为空且不超过200字'
      }).then(({ value }) => {
        return updateConsultantAuditStatus(row.id, '2', value);
      }).then(() => {
        getList();
        ElMessage.warning("已拒绝");
      });
    }
  });
}

/** 配置操作 */
function handleConfig(row) {
  currentConsultant.value = row;
  configTitle.value = `${row.name} - 门店配置`;
  loadConsultantConfig(row.id);
  configOpen.value = true;
}

/** 加载咨询师配置 */
function loadConsultantConfig(consultantId) {
  // 获取现有配置
  listConsultantConfig({ consultantId }).then(response => {
    const existingConfigs = response.rows || [];

    // 为每个门店创建配置项
    configForm.configs = storeList.value.map(store => {
      const existingConfig = existingConfigs.find(config => config.centerId === store.id);
      return {
        id: existingConfig?.id || null,
        consultantId: consultantId,
        centerId: store.id,
        arrivalTimeHours: existingConfig?.arrivalTimeHours || 2.0,
        enableArrivalFilter: existingConfig?.enableArrivalFilter || 1,
        status: existingConfig?.status || 1
      };
    });
  });
}

/** 获取门店列表 */
function getStoreList() {
  listStore().then(response => {
    storeList.value = response.rows || response.data || [];
  });
}

/** 获取门店名称 */
function getStoreName(storeId) {
  const store = storeList.value.find(s => s.id === storeId);
  return store ? store.name : `门店${storeId}`;
}

/** 处理到店时间变化 */
function handleArrivalTimeChange(config) {
  if (config.id) {
    setArrivalTime(config.consultantId, config.centerId, config.arrivalTimeHours).then(() => {
      ElMessage.success("到店时间更新成功");
    }).catch(() => {
      ElMessage.error("到店时间更新失败");
    });
  }
}

/** 处理过滤开关变化 */
function handleFilterChange(config) {
  if (config.id) {
    setArrivalFilterEnabled(config.consultantId, config.centerId, config.enableArrivalFilter === 1).then(() => {
      ElMessage.success("过滤设置更新成功");
    }).catch(() => {
      ElMessage.error("过滤设置更新失败");
    });
  }
}

/** 创建配置 */
function createConfig(config) {
  addConsultantConfig(config).then(response => {
    ElMessage.success("配置创建成功");
    // 重新加载配置
    loadConsultantConfig(config.consultantId);
  }).catch(() => {
    ElMessage.error("配置创建失败");
  });
}

/** 更新配置 */
function updateConfig(config) {
  updateConsultantConfig(config).then(() => {
    ElMessage.success("配置更新成功");
  }).catch(() => {
    ElMessage.error("配置更新失败");
  });
}

/** 为所有门店创建默认配置 */
function createAllDefaultConfigs() {
  const consultantId = currentConsultant.value.id;
  const promises = storeList.value.map(store => {
    return createDefaultConfig(consultantId, store.id);
  });

  Promise.all(promises).then(() => {
    ElMessage.success("已为所有门店创建默认配置");
    loadConsultantConfig(consultantId);
  }).catch(() => {
    ElMessage.error("创建默认配置失败");
  });
}

/** 关闭配置对话框 */
function submitConfig() {
  configOpen.value = false;
}

onMounted(() => {
  getStoreList();
  getList();
});
</script>

<style lang="scss" scoped>
.el-range-separator {
  padding: 0 10px;
}

.config-header {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  p {
    margin: 0;
    color: #606266;
    font-size: 14px;
    flex: 1;
  }
}
</style>
