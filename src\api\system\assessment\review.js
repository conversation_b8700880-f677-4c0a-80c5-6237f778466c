import request from '@/utils/request'

// 查询测评评价列表
export function listReview(query) {
  return request({
    url: '/system/assessment/review/list',
    method: 'get',
    params: query
  })
}

// 查询测评评价详细
export function getReview(id) {
  return request({
    url: '/system/assessment/review/' + id,
    method: 'get'
  })
}

// 新增测评评价
export function addReview(data) {
  return request({
    url: '/system/assessment/review',
    method: 'post',
    data: data
  })
}

// 修改测评评价
export function updateReview(data) {
  return request({
    url: '/system/assessment/review',
    method: 'put',
    data: data
  })
}

// 删除测评评价
export function delReview(ids) {
  return request({
    url: '/system/assessment/review/' + ids,
    method: 'delete'
  })
}

// 审核评价
export function auditReview(id, data) {
  return request({
    url: '/system/assessment/review/audit/' + id,
    method: 'put',
    data: data
  })
}

// 批量审核评价
export function batchAuditReview(data) {
  return request({
    url: '/system/assessment/review/audit/batch',
    method: 'put',
    data: data
  })
}

// 置顶评价
export function topReview(id, isTop) {
  return request({
    url: '/system/assessment/review/top/' + id,
    method: 'put',
    params: {
      isTop: isTop
    }
  })
}

// 获取待审核评价列表
export function getPendingReviews(query) {
  return request({
    url: '/system/assessment/review/pending',
    method: 'get',
    params: query
  })
}

// 评价统计信息
export function getReviewStatistics(query) {
  return request({
    url: '/system/assessment/review/statistics',
    method: 'get',
    params: query
  })
}

// 搜索评价
export function searchReview(query) {
  return request({
    url: '/system/assessment/review/search',
    method: 'get',
    params: query
  })
}

// 导出评价数据
export function exportReview(data) {
  return request({
    url: '/system/assessment/review/export',
    method: 'post',
    data: data
  })
}

// 回复评价
export function replyReview(id, replyContent) {
  return request({
    url: '/system/assessment/review/reply/' + id,
    method: 'put',
    params: {
      replyContent: replyContent
    }
  })
}

// 获取量表评价统计
export function getReviewStats(scaleId) {
  return request({
    url: '/system/assessment/review/stats/' + scaleId,
    method: 'get'
  })
}
