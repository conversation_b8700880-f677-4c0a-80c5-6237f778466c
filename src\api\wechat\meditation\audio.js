import request from '@/utils/request'

// 查询冥想音频列表
export function listMeditationAudio(meditationId, query) {
  return request({
    url: `/system/meditation/audio/list/${meditationId}`,
    method: 'get',
    params: query
  })
}

// 导出冥想音频列表
export function exportMeditationAudio(meditationId, query) {
  return request({
    url: `/system/meditation/audio/export/${meditationId}`,
    method: 'post',
    params: query
  })
}

// 获取冥想音频详细信息
export function getMeditationAudio(id) {
  return request({
    url: `/system/meditation/audio/${id}`,
    method: 'get'
  })
}

// 新增冥想音频
export function addMeditationAudio(data) {
  return request({
    url: '/system/meditation/audio',
    method: 'post',
    data: data
  })
}

// 修改冥想音频
export function updateMeditationAudio(data) {
  return request({
    url: '/system/meditation/audio',
    method: 'put',
    data: data
  })
}

// 删除冥想音频
export function delMeditationAudio(ids) {
  return request({
    url: `/system/meditation/audio/${ids}`,
    method: 'delete'
  })
}

// 批量删除冥想音频
export function batchDelMeditationAudio(ids) {
  return request({
    url: '/system/meditation/audio/batch',
    method: 'delete',
    data: ids
  })
}

// 更新音频状态
export function updateMeditationAudioStatus(id, status) {
  return request({
    url: `/system/meditation/audio/status/${id}`,
    method: 'put',
    data: { status }
  })
}

// 移动音频顺序
export function moveMeditationAudio(id, direction) {
  return request({
    url: `/system/meditation/audio/move/${id}`,
    method: 'put',
    data: { direction }
  })
}

// 批量更新音频顺序
export function batchUpdateAudioOrder(audioList) {
  return request({
    url: '/system/meditation/audio/batch-order',
    method: 'put',
    data: audioList
  })
}

// 增加音频播放次数
export function incrementAudioPlayCount(audioId) {
  return request({
    url: `/system/meditation/audio/play/${audioId}`,
    method: 'post'
  })
}

// 获取用户可播放的音频列表（考虑购买状态）
export function getPlayableAudios(meditationId) {
  return request({
    url: `/system/meditation/audio/playable/${meditationId}`,
    method: 'get'
  })
}

// 复制音频到其他冥想
export function copyAudioToMeditation(audioId, targetMeditationId) {
  return request({
    url: `/system/meditation/audio/copy/${audioId}`,
    method: 'post',
    data: { targetMeditationId }
  })
}

// 批量复制音频
export function batchCopyAudios(audioIds, targetMeditationId) {
  return request({
    url: '/system/meditation/audio/batch-copy',
    method: 'post',
    data: { audioIds, targetMeditationId }
  })
}

// 获取音频统计信息
export function getAudioStatistics(audioId) {
  return request({
    url: `/system/meditation/audio/statistics/${audioId}`,
    method: 'get'
  })
}

// 批量设置试听音频
export function batchSetTrialAudios(audioIds, isTrial, trialDuration) {
  return request({
    url: '/system/meditation/audio/batch-trial',
    method: 'put',
    data: { audioIds, isTrial, trialDuration }
  })
}

// 获取音频文件信息（文件大小、格式等）
export function getAudioFileInfo(audioUrl) {
  return request({
    url: '/system/meditation/audio/file-info',
    method: 'post',
    data: { audioUrl }
  })
}

// 分析音频文件获取详细信息（时长、比特率、采样率等）
export function analyzeAudioFile(audioUrl) {
  return request({
    url: '/system/meditation/audio/analyze',
    method: 'post',
    data: { audioUrl }
  })
}

// 验证音频文件
export function validateAudioFile(audioUrl) {
  return request({
    url: '/system/meditation/audio/validate',
    method: 'post',
    data: { audioUrl }
  })
}
