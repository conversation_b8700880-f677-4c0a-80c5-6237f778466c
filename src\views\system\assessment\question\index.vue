<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="量表" prop="scaleId">
        <el-select v-model="queryParams.scaleId" placeholder="请选择量表" clearable style="width: 240px">
          <el-option v-for="scale in scaleOptions" :key="scale.id" :label="scale.scaleName" :value="scale.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="题目内容" prop="questionText">
        <el-input v-model="queryParams.questionText" placeholder="请输入题目内容" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="题目类型" prop="questionType">
        <el-select v-model="queryParams.questionType" placeholder="请选择题目类型" clearable style="width: 240px">
          <el-option label="单选题" value="SINGLE" />
          <el-option label="多选题" value="MULTIPLE" />
          <el-option label="填空题" value="FILL" />
          <el-option label="判断题" value="JUDGE" />
        </el-select>
      </el-form-item>
      <el-form-item label="子量表" prop="subscaleRef">
        <el-input v-model="queryParams.subscaleRef" placeholder="请输入子量表" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="是否必答" prop="isRequired">
        <el-select v-model="queryParams.isRequired" placeholder="请选择是否必答" clearable style="width: 240px">
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:question:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:question:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:question:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Sort" @click="handleSort"
          v-hasPermi="['system:question:edit']">排序</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:question:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="questionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" width="80" />
      <el-table-column label="题号" align="center" prop="questionNo" width="80" />
      <el-table-column label="题目内容" align="center" :show-overflow-tooltip="true" min-width="200">
        <template #default="scope">
          {{ scope.row.content || scope.row.questionText }}
        </template>
      </el-table-column>
      <el-table-column label="题目类型" align="center" width="100">
        <template #default="scope">
          <el-tag :type="getQuestionTypeTag(scope.row.questionType)">
            {{ scope.row.questionTypeDesc || getQuestionTypeText(scope.row.questionType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否必答" align="center" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.isRequired ? 'danger' : 'info'">
            {{ scope.row.requiredDesc || (scope.row.isRequired ? '是' : '否') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="子量表" align="center" prop="subscaleRef" width="100" />
      <el-table-column label="反向计分" align="center" width="80">
        <template #default="scope">
          <el-tag v-if="scope.row.isReverse" type="warning">是</el-tag>
          <el-tag v-else type="success">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="sort" width="80" />
      <el-table-column label="选项数量" align="center" width="80">
        <template #default="scope">
          {{ scope.row.optionList ? scope.row.optionList.length : (scope.row.optionCount || 0) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.delFlag === '0' ? 'success' : 'danger'">
            {{ scope.row.delFlag === '0' ? '正常' : '已删除' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleViewOptions(scope.row)"
            v-hasPermi="['system:question:query']">选项</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:question:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:question:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改题目对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="questionRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属量表" prop="scaleId">
              <el-select v-model="form.scaleId" placeholder="请选择量表" style="width: 100%">
                <el-option v-for="scale in scaleOptions" :key="scale.id" :label="scale.scaleName" :value="scale.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="题号" prop="questionNo">
              <el-input-number v-model="form.questionNo" :min="1" :max="999" placeholder="请输入题号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="题目内容" prop="questionText">
          <el-input v-model="form.questionText" type="textarea" placeholder="请输入题目内容" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="题目类型" prop="questionType">
              <el-select v-model="form.questionType" placeholder="请选择题目类型">
                <el-option v-for="dict in question_type_options" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否必答" prop="isRequired">
              <el-radio-group v-model="form.isRequired">
                <el-radio v-for="dict in yes_no_options" :key="dict.value" :value="parseInt(dict.value)">{{ dict.label
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="子量表" prop="subscaleRef">
              <el-input v-model="form.subscaleRef" placeholder="请输入子量表" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="反向计分" prop="isReverse">
              <el-radio-group v-model="form.isReverse">
                <el-radio :value="0">否</el-radio>
                <el-radio :value="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="form.sort" :min="0" placeholder="排序" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="维度" prop="dimension">
              <el-input v-model="form.dimension" placeholder="请输入维度" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 选项部分 -->
        <el-divider content-position="center">选项设置</el-divider>
        <el-form-item v-for="(option, index) in form.options" :key="index" :label="'选项' + (index + 1)">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-input v-model="option.optionText" placeholder="请输入选项文本" />
            </el-col>
            <el-col :span="4">
              <el-input-number v-model="option.optionValue" :min="0" placeholder="分值" />
            </el-col>
            <el-col :span="4">
              <el-input-number v-model="option.sort" :min="0" placeholder="排序" />
            </el-col>
            <el-col :span="4">
              <el-button type="danger" icon="Delete" @click.prevent="removeOption(index)">删除</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addOption">添加选项</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选项查看对话框 -->
    <el-dialog title="题目选项管理" v-model="optionsOpen" width="1000px" append-to-body>
      <!-- 题目信息 -->
      <el-card class="box-card" style="margin-bottom: 20px;">
        <template #header>
          <span>题目信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="题号">{{ currentQuestion.questionNo }}</el-descriptions-item>
          <el-descriptions-item label="题目类型">
            <el-tag :type="getQuestionTypeTag(currentQuestion.questionType)">
              {{ currentQuestion.questionTypeDesc || getQuestionTypeText(currentQuestion.questionType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="题目内容" :span="2">
            {{ currentQuestion.content || currentQuestion.questionText }}
          </el-descriptions-item>
          <el-descriptions-item label="子量表">{{ currentQuestion.subscaleRef || '-' }}</el-descriptions-item>
          <el-descriptions-item label="是否必答">
            <el-tag :type="currentQuestion.isRequired ? 'danger' : 'info'">
              {{ currentQuestion.requiredDesc || (currentQuestion.isRequired ? '是' : '否') }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="反向计分">
            <el-tag v-if="currentQuestion.isReverse" type="warning">是</el-tag>
            <el-tag v-else type="success">否</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="排序">{{ currentQuestion.sort }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 选项列表 -->
      <el-card class="box-card">
        <template #header>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span>选项列表 ({{ optionsList.length }}个选项)</span>
            <el-button type="primary" size="small" icon="Plus" @click="handleAddOption"
              v-hasPermi="['system:question:edit']">新增选项</el-button>
          </div>
        </template>
        <el-table :data="optionsList" style="width: 100%" v-if="optionsList.length > 0">
          <el-table-column label="选项文本" align="center" prop="optionText" />
          <el-table-column label="选项分值" align="center" prop="optionValue" width="100" />
          <el-table-column label="排序" align="center" prop="sort" width="80" />
          <el-table-column label="状态" align="center" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                {{ scope.row.status === 1 ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="删除标志" align="center" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.delFlag === '0' ? 'success' : 'danger'">
                {{ scope.row.delFlag === '0' ? '正常' : '已删除' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" width="150">
            <template #default="scope">
              {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handleEditOption(scope.row)"
                v-hasPermi="['system:question:edit']">编辑</el-button>
              <el-button link type="danger" size="small" @click="handleDeleteOption(scope.row)"
                v-hasPermi="['system:question:remove']">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-empty v-else description="暂无选项数据">
          <el-button type="primary" @click="handleAddOption"
            v-hasPermi="['system:assessment:question:edit']">新增选项</el-button>
        </el-empty>
      </el-card>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="optionsOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选项编辑对话框 -->
    <el-dialog :title="optionTitle" v-model="optionFormOpen" width="600px" append-to-body>
      <el-form ref="optionFormRef" :model="optionForm" :rules="optionRules" label-width="120px">
        <el-form-item label="选项文本" prop="optionText">
          <el-input v-model="optionForm.optionText" placeholder="请输入选项文本" />
        </el-form-item>
        <el-form-item label="选项分值" prop="optionValue">
          <el-input-number v-model="optionForm.optionValue" :min="0" :max="100" placeholder="请输入选项分值"
            style="width: 100%" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="optionForm.sort" :min="0" placeholder="请输入排序" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="optionForm.status">
            <el-radio :value="1">正常</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-radio-group v-model="optionForm.delFlag">
            <el-radio value="0">正常</el-radio>
            <el-radio value="1">已删除</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitOptionForm">确 定</el-button>
          <el-button @click="cancelOption">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 题目排序对话框 -->
    <el-dialog title="题目排序" v-model="sortOpen" width="800px" append-to-body>
      <el-form :model="sortForm" label-width="100px">
        <el-form-item label="所属量表">
          <el-select v-model="sortForm.scaleId" placeholder="请选择量表" @change="getSortQuestions" style="width: 100%">
            <el-option v-for="scale in scaleOptions" :key="scale.id" :label="scale.scaleName" :value="scale.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-table v-if="sortForm.scaleId" :data="sortQuestions" row-key="id">
        <el-table-column label="题号" width="80">
          <template #default="scope">
            <el-input-number v-model="scope.row.questionNo" :min="1" :max="999" />
          </template>
        </el-table-column>
        <el-table-column label="题目内容" prop="questionText" :show-overflow-tooltip="true" />
        <el-table-column label="操作" width="160">
          <template #default="scope">
            <el-button link type="primary" icon="Top" @click="moveUp(scope.$index)"
              :disabled="scope.$index === 0">上移</el-button>
            <el-button link type="primary" icon="Bottom" @click="moveDown(scope.$index)"
              :disabled="scope.$index === sortQuestions.length - 1">下移</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitSort">保存排序</el-button>
          <el-button @click="sortOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Question">
import { listQuestion, getQuestion, delQuestion, addQuestion, updateQuestion, exportQuestion, getQuestionsByScale, updateQuestionOrder } from "@/api/system/assessment/question";
import { getOptionsByQuestion, addOption as addOptionApi, updateOption, delOption } from "@/api/system/assessment/option";
import { listScale } from "@/api/system/assessment/scale";

const { proxy } = getCurrentInstance();

const questionList = ref([]);
const scaleOptions = ref([]);
const optionsList = ref([]);
const sortQuestions = ref([]);
const open = ref(false);
const optionsOpen = ref(false);
const sortOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const currentQuestion = ref({});

// 选项编辑相关变量
const optionFormOpen = ref(false);
const optionTitle = ref("");
const optionForm = ref({
  id: null,
  questionId: null,
  optionText: "",
  optionValue: null,
  sort: 0,
  status: 1,
  delFlag: "0"
});
const optionRules = ref({
  optionText: [
    { required: true, message: "选项文本不能为空", trigger: "blur" }
  ],
  optionValue: [
    { required: true, message: "选项分值不能为空", trigger: "blur" }
  ]
});

const data = reactive({
  form: {
    options: []
  },
  sortForm: {
    scaleId: null
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    scaleId: null,
    questionText: null,
    questionType: null,
    subscaleRef: null,
    isRequired: null
  },
  rules: {
    scaleId: [
      { required: true, message: "所属量表不能为空", trigger: "change" }
    ],
    questionNo: [
      { required: true, message: "题号不能为空", trigger: "blur" }
    ],
    questionText: [
      { required: true, message: "题目内容不能为空", trigger: "blur" }
    ],
    questionType: [
      { required: true, message: "题目类型不能为空", trigger: "change" }
    ],
    isRequired: [
      { required: true, message: "是否必答不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, sortForm, rules } = toRefs(data);

/** 查询题目列表 */
function getList() {
  loading.value = true;
  // 从URL参数中获取scaleId
  const urlParams = new URLSearchParams(window.location.search);
  const scaleIdParam = urlParams.get('scaleId');
  if (scaleIdParam && !queryParams.value.scaleId) {
    queryParams.value.scaleId = parseInt(scaleIdParam);
  }

  listQuestion(queryParams.value).then(response => {
    questionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询量表列表 */
function getScaleList() {
  listScale().then(response => {
    scaleOptions.value = response.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    scaleId: null,
    questionNo: null,
    questionText: null,
    questionType: 1,
    isRequired: 1,
    dimension: null,
    options: []
  };

  // 从URL参数中获取scaleId
  const urlParams = new URLSearchParams(window.location.search);
  const scaleIdParam = urlParams.get('scaleId');
  if (scaleIdParam) {
    form.value.scaleId = parseInt(scaleIdParam);
  }

  proxy.resetForm("questionRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 保留URL中的scaleId参数
  const urlParams = new URLSearchParams(window.location.search);
  const scaleIdParam = urlParams.get('scaleId');
  if (scaleIdParam) {
    queryParams.value.scaleId = parseInt(scaleIdParam);
  }
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  // 添加默认选项
  addDefaultOptions();
  open.value = true;
  title.value = "添加题目";
}

/** 添加默认选项 */
function addDefaultOptions() {
  form.value.options = [
    { optionText: "非常同意", optionValue: 5, sort: 1, status: 1, delFlag: "0" },
    { optionText: "同意", optionValue: 4, sort: 2, status: 1, delFlag: "0" },
    { optionText: "不确定", optionValue: 3, sort: 3, status: 1, delFlag: "0" },
    { optionText: "不同意", optionValue: 2, sort: 4, status: 1, delFlag: "0" },
    { optionText: "非常不同意", optionValue: 1, sort: 5, status: 1, delFlag: "0" }
  ];
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getQuestion(_id).then(response => {
    form.value = response.data;
    // 获取选项
    getOptionsByQuestion(_id).then(res => {
      form.value.options = res.data || [];
    });
    open.value = true;
    title.value = "修改题目";
  });
}

/** 查看选项 */
function handleViewOptions(row) {
  currentQuestion.value = row;
  // 如果已有选项数据，直接使用；否则从后端获取
  if (row.optionList && row.optionList.length > 0) {
    optionsList.value = row.optionList;
    optionsOpen.value = true;
  } else {
    getOptionsByQuestion(row.id).then(response => {
      optionsList.value = response.data || [];
      optionsOpen.value = true;
    });
  }
}

/** 新增选项 */
function handleAddOption() {
  resetOptionForm();
  optionForm.value.questionId = currentQuestion.value.id;
  optionTitle.value = "新增选项";
  optionFormOpen.value = true;
}

/** 编辑选项 */
function handleEditOption(row) {
  resetOptionForm();
  optionForm.value = { ...row };
  optionTitle.value = "编辑选项";
  optionFormOpen.value = true;
}

/** 删除选项 */
function handleDeleteOption(row) {
  proxy.$modal.confirm('是否确认删除该选项？').then(function () {
    return delOption(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功");
    // 刷新当前题目的选项列表
    refreshCurrentQuestionOptions();
  }).catch(() => { });
}

/** 重置选项表单 */
function resetOptionForm() {
  optionForm.value = {
    id: null,
    questionId: null,
    optionText: "",
    optionValue: null,
    sort: 0,
    status: 1,
    delFlag: "0"
  };
  proxy.resetForm("optionFormRef");
}

/** 取消选项编辑 */
function cancelOption() {
  optionFormOpen.value = false;
  resetOptionForm();
}

/** 提交选项表单 */
function submitOptionForm() {
  proxy.$refs["optionFormRef"].validate(valid => {
    if (valid) {
      if (optionForm.value.id != null) {
        updateOption(optionForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          optionFormOpen.value = false;
          refreshCurrentQuestionOptions();
        });
      } else {
        addOptionApi(optionForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          optionFormOpen.value = false;
          refreshCurrentQuestionOptions();
        });
      }
    }
  });
}

/** 刷新当前题目的选项列表 */
function refreshCurrentQuestionOptions() {
  if (currentQuestion.value.id) {
    getOptionsByQuestion(currentQuestion.value.id).then(response => {
      optionsList.value = response.data || [];
      // 同时更新题目列表中的选项数量
      const questionIndex = questionList.value.findIndex(q => q.id === currentQuestion.value.id);
      if (questionIndex !== -1) {
        questionList.value[questionIndex].optionList = response.data || [];
      }
    });
  }
}

/** 添加选项 */
function addOption() {
  const newOrder = form.value.options.length + 1;
  const newOption = {
    optionText: "",
    optionValue: newOrder,
    sort: newOrder,
    status: 1,
    delFlag: "0"
  };
  form.value.options.push(newOption);
}

/** 删除选项 */
function removeOption(index) {
  form.value.options.splice(index, 1);
  // 重新排序
  form.value.options.forEach((item, idx) => {
    item.orderNum = idx + 1;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["questionRef"].validate(valid => {
    if (valid) {
      // 验证选项
      if (form.value.options.length === 0) {
        proxy.$modal.msgError("请至少添加一个选项");
        return;
      }

      // 验证选项内容
      for (let i = 0; i < form.value.options.length; i++) {
        if (!form.value.options[i].optionText) {
          proxy.$modal.msgError(`选项${i + 1}的文本不能为空`);
          return;
        }
      }

      if (form.value.id != null) {
        updateQuestion(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addQuestion(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除题目编号为"' + _ids + '"的数据项？').then(function () {
    return delQuestion(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/assessment/question/export', {
    ...queryParams.value
  }, `question_${new Date().getTime()}.xlsx`)
}

/** 排序按钮操作 */
function handleSort() {
  sortForm.value.scaleId = null;
  sortQuestions.value = [];
  sortOpen.value = true;
}

/** 获取排序题目 */
function getSortQuestions() {
  if (!sortForm.value.scaleId) return;

  getQuestionsByScale(sortForm.value.scaleId).then(response => {
    sortQuestions.value = response.data || [];
    // 按题号排序
    sortQuestions.value.sort((a, b) => a.questionNo - b.questionNo);
  });
}

/** 上移题目 */
function moveUp(index) {
  if (index === 0) return;
  const temp = sortQuestions.value[index];
  sortQuestions.value[index] = sortQuestions.value[index - 1];
  sortQuestions.value[index - 1] = temp;
  // 更新题号
  updateQuestionNumbers();
}

/** 下移题目 */
function moveDown(index) {
  if (index === sortQuestions.value.length - 1) return;
  const temp = sortQuestions.value[index];
  sortQuestions.value[index] = sortQuestions.value[index + 1];
  sortQuestions.value[index + 1] = temp;
  // 更新题号
  updateQuestionNumbers();
}

/** 更新题号 */
function updateQuestionNumbers() {
  sortQuestions.value.forEach((item, index) => {
    item.questionNo = index + 1;
  });
}

/** 提交排序 */
function submitSort() {
  const data = {
    scaleId: sortForm.value.scaleId,
    questions: sortQuestions.value.map(item => ({
      id: item.id,
      questionNo: item.questionNo
    }))
  };

  updateQuestionOrder(data).then(() => {
    proxy.$modal.msgSuccess("排序保存成功");
    sortOpen.value = false;
    getList();
  });
}

/** 获取题目类型标签样式 */
function getQuestionTypeTag(questionType) {
  const typeMap = {
    'SINGLE': 'primary',
    'MULTIPLE': 'success',
    'FILL': 'warning',
    'JUDGE': 'info'
  };
  return typeMap[questionType] || 'info';
}

/** 获取题目类型文本 */
function getQuestionTypeText(questionType) {
  const typeMap = {
    'SINGLE': '单选题',
    'MULTIPLE': '多选题',
    'FILL': '填空题',
    'JUDGE': '判断题'
  };
  return typeMap[questionType] || questionType;
}

onMounted(() => {
  getList();
  getScaleList();
});
</script>

<style scoped>
.el-divider {
  margin: 20px 0;
}
</style>
