import request from '@/utils/request'

// 查询订单列表
export function listAssessmentOrder(query) {
  return request({
    url: '/system/assessment/order/list',
    method: 'get',
    params: query
  })
}

// 查询订单详情
export function getAssessmentOrder(id) {
  return request({
    url: '/system/assessment/order/' + id,
    method: 'get'
  })
}

// 新增订单
export function addAssessmentOrder(data) {
  return request({
    url: '/system/assessment/order',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateAssessmentOrder(data) {
  return request({
    url: '/system/assessment/order',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delAssessmentOrder(ids) {
  return request({
    url: '/system/assessment/order/' + ids,
    method: 'delete'
  })
}

// 处理退款
export function refundAssessmentOrder(id, data) {
  return request({
    url: '/system/assessment/order/refund/' + id,
    method: 'post',
    data: data
  })
}

// 订单统计
export function getAssessmentOrderStats(query) {
  return request({
    url: '/system/assessment/order/stats',
    method: 'get',
    params: query
  })
}

// 导出订单数据
export function exportAssessmentOrder(query) {
  return request({
    url: '/system/assessment/order/export',
    method: 'post',
    params: query
  })
}

// 根据订单号查询订单
export function getAssessmentOrderByOrderNo(orderNo) {
  return request({
    url: '/system/assessment/order/orderNo/' + orderNo,
    method: 'get'
  })
}

// 更新订单状态
export function updateAssessmentOrderStatus(id, status) {
  return request({
    url: '/system/assessment/order/status/' + id,
    method: 'post',
    params: {
      status: status
    }
  })
}

// 取消订单
export function cancelAssessmentOrder(id, cancelReason) {
  return request({
    url: '/system/assessment/order/cancel/' + id,
    method: 'post',
    params: {
      cancelReason: cancelReason
    }
  })
}

// 根据用户ID查询订单列表
export function getAssessmentOrdersByUser(userId) {
  return request({
    url: '/system/assessment/order/user/' + userId,
    method: 'get'
  })
}

// 根据量表ID查询订单列表
export function getAssessmentOrdersByScale(scaleId) {
  return request({
    url: '/system/assessment/order/scale/' + scaleId,
    method: 'get'
  })
}

// 获取订单收入统计
export function getAssessmentOrderIncome(startTime, endTime) {
  return request({
    url: '/system/assessment/order/income',
    method: 'get',
    params: {
      startTime: startTime,
      endTime: endTime
    }
  })
}

// 获取热门量表统计
export function getPopularScales(query) {
  return request({
    url: '/system/assessment/order/popular-scales',
    method: 'get',
    params: query
  })
}
