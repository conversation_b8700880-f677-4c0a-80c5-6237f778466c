# 测评系统API接口更新说明

## 更新概述

根据提供的接口文档，已完成对测评系统相关API接口的更新和完善，包括量表管理、订单管理和评价管理三个模块。

## 1. 量表管理接口更新

### 文件位置
- API文件：`src/api/system/assessment/scale.js`

### 主要更新内容
1. **接口路径调整**：将接口路径从 `/system/assessment/scale` 调整为 `/system/scale`
2. **发布/下架接口**：
   - 发布量表：`POST /system/scale/{id}/publish`
   - 下架量表：`POST /system/scale/{id}/unpublish`（原名为offlineScale，已重命名为unpublishScale）

### 接口列表
| 功能 | 方法 | 路径 | 函数名 |
|------|------|------|--------|
| 查询量表列表 | GET | `/system/scale/list` | listScale |
| 获取量表详情 | GET | `/system/scale/{id}` | getScale |
| 新增量表 | POST | `/system/scale` | addScale |
| 修改量表 | PUT | `/system/scale` | updateScale |
| 删除量表 | DELETE | `/system/scale/{ids}` | delScale |
| 发布量表 | POST | `/system/scale/{id}/publish` | publishScale |
| 下架量表 | POST | `/system/scale/{id}/unpublish` | unpublishScale |

## 2. 订单管理接口（新增）

### 文件位置
- API文件：`src/api/system/assessment/order.js`（新建）
- 页面文件：`src/views/system/assessment/order/index.vue`（新建）

### 主要功能
1. **基础CRUD操作**：查询、新增、修改、删除订单
2. **业务功能**：退款处理、订单统计、状态管理
3. **数据导出**：支持订单数据导出

### 接口列表
| 功能 | 方法 | 路径 | 函数名 |
|------|------|------|--------|
| 查询订单列表 | GET | `/system/assessment/order/list` | listAssessmentOrder |
| 查询订单详情 | GET | `/system/assessment/order/{id}` | getAssessmentOrder |
| 处理退款 | POST | `/system/assessment/order/refund/{id}` | refundAssessmentOrder |
| 订单统计 | GET | `/system/assessment/order/stats` | getAssessmentOrderStats |

### 页面功能
- 订单列表展示（支持多条件筛选）
- 订单详情查看
- 退款申请处理
- 订单状态管理
- 数据导出功能

## 3. 评价管理接口更新

### 文件位置
- API文件：`src/api/system/assessment/review.js`
- 页面文件：`src/views/system/assessment/review/index.vue`

### 主要更新内容
1. **新增API接口**：
   - 批量审核评价：`batchAuditReview`
   - 置顶评价：`topReview`
   - 获取待审核评价：`getPendingReviews`
   - 评价统计：`getReviewStatistics`
   - 搜索评价：`searchReview`

2. **页面功能增强**：
   - 添加关键词搜索功能
   - 添加置顶/取消置顶功能
   - 添加待审核评价快速查看
   - 优化批量审核功能

### 接口列表
| 功能 | 方法 | 路径 | 函数名 |
|------|------|------|--------|
| 查询评价列表 | GET | `/system/assessment/review/list` | listReview |
| 获取评价详情 | GET | `/system/assessment/review/{id}` | getReview |
| 审核评价 | PUT | `/system/assessment/review/audit/{id}` | auditReview |
| 批量审核评价 | PUT | `/system/assessment/review/audit/batch` | batchAuditReview |
| 置顶评价 | PUT | `/system/assessment/review/top/{id}` | topReview |
| 删除评价 | DELETE | `/system/assessment/review/{ids}` | delReview |
| 查询待审核评价 | GET | `/system/assessment/review/pending` | getPendingReviews |
| 评价统计 | GET | `/system/assessment/review/statistics` | getReviewStatistics |
| 搜索评价 | GET | `/system/assessment/review/search` | searchReview |
| 导出评价数据 | POST | `/system/assessment/review/export` | exportReview |

## 4. 技术实现说明

### 前端技术栈
- Vue 3 + Composition API
- Element Plus UI组件库
- Axios HTTP客户端

### 代码规范
- 使用ES6+语法
- 遵循Vue 3 Composition API最佳实践
- 统一的错误处理和用户反馈
- 响应式数据管理

### 权限控制
- 使用`v-hasPermi`指令进行权限控制
- 按钮级别的权限管理
- 接口调用权限验证

## 5. 后续工作建议

1. **后端接口开发**：根据前端API调用，开发对应的后端接口
2. **数据字典配置**：配置订单状态、支付状态等数据字典
3. **权限配置**：在系统中配置相应的权限标识
4. **测试验证**：进行功能测试和接口联调
5. **文档完善**：补充接口文档和使用说明

## 6. 注意事项

1. **接口路径**：请确保后端接口路径与前端调用保持一致
2. **数据格式**：注意请求和响应数据格式的统一
3. **错误处理**：建议统一错误码和错误信息格式
4. **性能优化**：对于大数据量的列表查询，建议实现分页和索引优化

## 7. 文件清单

### 新增文件
- `src/api/system/assessment/order.js` - 订单管理API
- `src/views/system/assessment/order/index.vue` - 订单管理页面
- `docs/测评系统API接口更新说明.md` - 本文档

### 修改文件
- `src/api/system/assessment/scale.js` - 量表管理API更新
- `src/api/system/assessment/review.js` - 评价管理API更新
- `src/views/system/assessment/review/index.vue` - 评价管理页面更新

---

**更新时间**：2025-01-21  
**更新人员**：AI Assistant  
**版本**：v1.0
