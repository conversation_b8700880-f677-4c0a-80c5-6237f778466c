<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="冥想名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入冥想名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择分类" clearable style="width: 240px">
          <el-option v-for="category in categoryList" :key="category.categoryId" :label="category.categoryName"
            :value="category.categoryId" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择类型" clearable style="width: 240px">
          <el-option label="引导冥想" value="1" />
          <el-option label="音乐冥想" value="2" />
          <el-option label="自然声音" value="3" />
          <el-option label="白噪音" value="4" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 240px">
          <el-option label="草稿" value="0" />
          <el-option label="已发布" value="1" />
          <el-option label="已下架" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:meditation:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:meditation:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:meditation:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:meditation:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="meditationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="冥想ID" align="center" prop="id" width="80" />
      <el-table-column label="封面" align="center" prop="coverImage" width="100">
        <template #default="scope">
          <image-preview :src="scope.row.coverImage" :width="50" :height="50" v-if="scope.row.coverImage" />
        </template>
      </el-table-column>
      <el-table-column label="冥想名称" align="center" prop="title" :show-overflow-tooltip="true" />
      <el-table-column label="分类" align="center" prop="categoryName">
        <template #default="scope">
          <span v-if="scope.row.categories && scope.row.categories.length > 0">
            {{ scope.row.categories[0].categoryName }}
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="类型" align="center" prop="type" width="100">
        <template #default="scope">
          <dict-tag :options="sys_meditation_type" :value="scope.row.type" />
        </template>
      </el-table-column> -->
      <el-table-column label="时长" align="center" prop="duration" width="100">
        <template #default="scope">
          <div v-if="scope.row.totalDuration">
            <span>{{ formatDuration(scope.row.totalDuration) }}</span>
            <el-tag size="small" type="info" class="ml-1">{{ scope.row.audioCount || 0 }}个音频</el-tag>
          </div>
          <span v-else>{{ scope.row.duration }}分钟</span>
        </template>
      </el-table-column>
      <el-table-column label="试听" align="center" prop="hasTrial" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.hasTrial ? 'success' : 'info'" size="small">
            {{ scope.row.hasTrial ? '有' : '无' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="播放次数" align="center" prop="playCount" width="100" />
      <el-table-column label="评分" align="center" prop="ratingAvg" width="120">
        <template #default="scope">
          <el-rate v-model="scope.row.ratingAvg" disabled show-score text-color="#ff9900" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_course_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="250">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)"
            v-hasPermi="['system:meditation:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:meditation:edit']">修改</el-button>
          <el-button link type="success" icon="Headphone" @click="handleAudioManage(scope.row)"
            v-hasPermi="['system:meditation:audio:query']">音频</el-button>
          <el-dropdown @command="(command) => handleCommand(command, scope.row)">
            <el-button link type="primary">
              更多<el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="delete" icon="Delete">删除</el-dropdown-item>
                <el-dropdown-item command="publish" v-if="scope.row.status == 0" icon="VideoPlay">发布</el-dropdown-item>
                <el-dropdown-item command="unpublish" v-if="scope.row.status == 1" icon="VideoPause">下架
                </el-dropdown-item>
                <el-dropdown-item command="updateRating" icon="Star">更新评分</el-dropdown-item>
                <el-dropdown-item command="statistics" icon="DataAnalysis">统计信息</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改冥想对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="meditationRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="冥想名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入冥想名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="副标题" prop="subtitle">
              <el-input v-model="form.subtitle" placeholder="请输入副标题" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分类" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择分类" style="width: 100%">
                <el-option v-for="category in categoryList" :key="category.categoryId" :label="category.categoryName"
                  :value="category.categoryId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
                <el-option v-for="dict in sys_meditation_type" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item> -->
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="时长(分钟)" prop="duration">
              <el-input-number v-model="form.duration" :min="1" :max="999" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="难度等级" prop="difficultyLevel">
              <el-select v-model="form.difficultyLevel" placeholder="请选择难度等级" style="width: 100%">
                <el-option v-for="item in sys_difficulty_level" :key="item.value" :label="item.label"
                  :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="冥想封面" prop="coverImage">
          <image-upload v-model="form.coverImage" />
        </el-form-item>
        <el-form-item label="音频文件" prop="audioUrl">
          <file-upload v-model="form.audioUrl" :file-type="['mp3', 'wav', 'flac']" />
        </el-form-item>
        <el-form-item label="冥想描述" prop="description">
          <editor v-model="form.description" :min-height="192" />
        </el-form-item>
        <el-form-item label="引导词" prop="guideText">
          <editor v-model="form.guideText" :min-height="192" />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input v-model="form.tags" placeholder="请输入标签，多个标签用逗号分隔" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 冥想详情对话框 -->
    <el-dialog title="冥想详情" v-model="detailOpen" width="1000px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="冥想名称">{{ detailForm.title }}</el-descriptions-item>
        <el-descriptions-item label="副标题">{{ detailForm.subtitle }}</el-descriptions-item>
        <!-- <el-descriptions-item label="分类">{{ detailForm.categories[0].categoryName }}</el-descriptions-item> -->
        <!-- <el-descriptions-item label="类型">
          <dict-tag :options="sys_meditation_type" :value="detailForm.type" />
        </el-descriptions-item> -->
        <el-descriptions-item label="时长">{{ detailForm.duration }}分钟</el-descriptions-item>
        <el-descriptions-item label="难度等级">
          <dict-tag :options="sys_difficulty_level" :value="detailForm.difficultyLevel" />
        </el-descriptions-item>
        <el-descriptions-item label="播放次数">{{ detailForm.playCount }}</el-descriptions-item>
        <el-descriptions-item label="平均评分">{{ detailForm.ratingAvg }}</el-descriptions-item>
        <el-descriptions-item label="评价数量">{{ detailForm.ratingCount }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="sys_course_status" :value="detailForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(detailForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailForm.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="冥想封面" :span="2">
          <image-preview :src="detailForm.coverImage" :width="200" :height="150" v-if="detailForm.coverImage" />
        </el-descriptions-item>
        <el-descriptions-item label="音频文件" :span="2">
          <audio v-if="detailForm.audioUrl" controls style="width: 100%">
            <source :src="detailForm.audioUrl" type="audio/mpeg">
            您的浏览器不支持音频播放。
          </audio>
        </el-descriptions-item>
        <el-descriptions-item label="冥想描述" :span="2">
          <div v-html="detailForm.description"></div>
        </el-descriptions-item>
        <el-descriptions-item label="引导词" :span="2">
          <div v-html="detailForm.guideText"></div>
        </el-descriptions-item>
        <el-descriptions-item label="标签" :span="2">{{ detailForm.tags }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog title="冥想统计信息" v-model="statisticsOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="播放次数">{{ statisticsData.playCount }}</el-descriptions-item>
        <el-descriptions-item label="平均评分">{{ statisticsData.ratingAvg }}</el-descriptions-item>
        <el-descriptions-item label="评价数量">{{ statisticsData.ratingCount }}</el-descriptions-item>
        <el-descriptions-item label="最后更新">{{ parseTime(new Date()) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="Meditation">
import {
  listMeditation, getMeditation, delMeditation, addMeditation, updateMeditation, exportMeditation,
  publishMeditation, unpublishMeditation, updateMeditationRating, getMeditationStatistics
} from "@/api/wechat/meditation/meditation";
import { listCategory } from "@/api/wechat/category";

const { proxy } = getCurrentInstance();
const { sys_meditation_type, sys_meditation_status, sys_difficulty_level, sys_course_status } = proxy.useDict('sys_meditation_type', 'sys_meditation_status', 'sys_difficulty_level', 'sys_course_status');

const router = useRouter();

const meditationList = ref([]);
const categoryList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const statisticsOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  detailForm: {},
  statisticsData: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: null,
    categoryId: null,
    type: null,
    status: null
  },
  rules: {
    title: [
      { required: true, message: "冥想名称不能为空", trigger: "blur" }
    ],
    categoryId: [
      { required: true, message: "分类不能为空", trigger: "change" }
    ],
    type: [
      { required: true, message: "类型不能为空", trigger: "change" }
    ],
    duration: [
      { required: true, message: "时长不能为空", trigger: "blur" }
    ],
    audioUrl: [
      { required: true, message: "音频文件不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, detailForm, statisticsData, rules } = toRefs(data);

/** 查询冥想列表 */
function getList() {
  loading.value = true;
  listMeditation(queryParams.value).then(response => {
    meditationList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询分类列表 */
function getCategoryList() {
  listCategory().then(response => {
    categoryList.value = response.data.find(item => item.categoryId == 14).children;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    title: null,
    subtitle: null,
    description: null,
    guideText: null,
    coverImage: null,
    audioUrl: null,
    categoryId: null,
    type: null,
    duration: null,
    difficultyLevel: null,
    tags: null,
    status: "0",
    remark: null
  };
  proxy.resetForm("meditationRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加冥想";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getMeditation(_id).then(response => {
    form.value = response.data;
    form.value.difficultyLevel = '' + response.data.difficultyLevel;
    // 设置分类ID - 优先从categories数组获取，其次从categoryIds数组获取
    if (response.data.categories && response.data.categories.length > 0) {
      form.value.categoryId = response.data.categories[0].categoryId;
    } else if (response.data.categoryIds && response.data.categoryIds.length > 0) {
      form.value.categoryId = response.data.categoryIds[0];
    }
    open.value = true;
    title.value = "修改冥想";
  });
}

/** 详情按钮操作 */
function handleDetail(row) {
  const _id = row.id;
  getMeditation(_id).then(response => {
    detailForm.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["meditationRef"].validate(valid => {
    if (valid) {
      // 处理分类ID，将单个categoryId转换为categoryIds数组
      const submitData = { ...form.value };
      if (submitData.categoryId) {
        submitData.categoryIds = [submitData.categoryId];
      }

      if (form.value.id != null) {
        updateMeditation(submitData).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMeditation(submitData).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除冥想编号为"' + _ids + '"的数据项？').then(function () {
    return delMeditation(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/meditation/export', {
    ...queryParams.value
  }, `meditation_${new Date().getTime()}.xlsx`)
}

/** 音频管理 */
function handleAudioManage(row) {
  router.push(`/wechat/group-order-classification/meditation-system/audio/${row.id}`);
}

/** 格式化时长 */
function formatDuration(seconds) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case 'delete':
      handleDelete(row);
      break;
    case 'publish':
      handlePublish(row);
      break;
    case 'unpublish':
      handleUnpublish(row);
      break;
    case 'updateRating':
      handleUpdateRating(row);
      break;
    case 'statistics':
      handleStatistics(row);
      break;
  }
}

/** 发布冥想 */
function handlePublish(row) {
  proxy.$modal.confirm('是否确认发布冥想"' + row.title + '"？').then(function () {
    return publishMeditation(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发布成功");
  }).catch(() => { });
}

/** 下架冥想 */
function handleUnpublish(row) {
  proxy.$modal.confirm('是否确认下架冥想"' + row.title + '"？').then(function () {
    return unpublishMeditation(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("下架成功");
  }).catch(() => { });
}

/** 更新评分 */
function handleUpdateRating(row) {
  updateMeditationRating(row.id).then(response => {
    getList();
    proxy.$modal.msgSuccess("评分更新成功");
  });
}

/** 查看统计信息 */
function handleStatistics(row) {
  getMeditationStatistics(row.id).then(response => {
    statisticsData.value = response.data;
    statisticsOpen.value = true;
  });
}

onMounted(() => {
  getList();
  getCategoryList();
});
</script>
