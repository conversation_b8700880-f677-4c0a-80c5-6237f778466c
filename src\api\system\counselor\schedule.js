import request from '@/utils/request'

// 查询咨询师排班列表
export function listSchedule(query) {
  return request({
    url: '/system/counselor/schedule/list',
    method: 'get',
    params: query
  })
}

// 获取咨询师排班详细信息
export function getSchedule(id) {
  return request({
    url: '/system/counselor/schedule/' + id,
    method: 'get'
  })
}

// 根据咨询师和日期查询排班
export function getScheduleByCounselorAndDate(counselorId, scheduleDate) {
  return request({
    url: `/system/counselor/schedule/counselor/${counselorId}`,
    method: 'get',
    params: { scheduleDate }
  })
}

// 查询咨询师在日期范围内的排班
export function getSchedulesByDateRange(counselorId, startDate, endDate) {
  return request({
    url: `/system/counselor/schedule/counselor/${counselorId}/range`,
    method: 'get',
    params: { startDate, endDate }
  })
}

// 查询指定日期的所有咨询师排班
export function getSchedulesByDate(scheduleDate, centerId) {
  return request({
    url: `/system/counselor/schedule/date/${scheduleDate}`,
    method: 'get',
    params: { centerId }
  })
}

// 新增咨询师排班
export function addSchedule(data) {
  return request({
    url: '/system/counselor/schedule',
    method: 'post',
    data: data
  })
}

// 批量新增咨询师排班
export function batchAddSchedule(data) {
  return request({
    url: '/system/counselor/schedule/batch',
    method: 'post',
    data: data
  })
}

// 修改咨询师排班
export function updateSchedule(data) {
  return request({
    url: '/system/counselor/schedule',
    method: 'put',
    data: data
  })
}

// 删除咨询师排班
export function delSchedule(ids) {
  return request({
    url: '/system/counselor/schedule/' + ids,
    method: 'delete'
  })
}

// 删除指定日期范围的排班
export function delScheduleByDateRange(counselorId, startDate, endDate) {
  return request({
    url: `/system/counselor/schedule/counselor/${counselorId}/range`,
    method: 'delete',
    params: { startDate, endDate }
  })
}

// 为咨询师生成默认排班
export function generateDefaultSchedule(counselorId, startDate, endDate, centerId = 1) {
  return request({
    url: `/system/counselor/schedule/generate/${counselorId}`,
    method: 'post',
    params: { startDate, endDate, centerId }
  })
}

// 为所有咨询师生成默认排班
export function generateAllSchedule(startDate, endDate, centerId = 1) {
  return request({
    url: '/system/counselor/schedule/generateAll',
    method: 'post',
    params: { startDate, endDate, centerId }
  })
}

// 检查排班是否存在
export function checkScheduleExists(counselorId, scheduleDate) {
  return request({
    url: '/system/counselor/schedule/check',
    method: 'get',
    params: { counselorId, scheduleDate }
  })
}
